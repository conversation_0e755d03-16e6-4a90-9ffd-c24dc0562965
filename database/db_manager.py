import asyncio
import logging
import sqlite3
import os
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database operations"""

    def __init__(self):
        self.db_path = os.getenv('DATABASE_URL', 'sqlite:///trading_bot.db').replace('sqlite:///', '')
        self.connection = None

    async def initialize(self):
        """Initialize database"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row

            # Create tables
            await self._create_tables()
            logger.info("✅ Database initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            return False

    async def _create_tables(self):
        """Create database tables"""
        cursor = self.connection.cursor()

        # Trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                amount REAL NOT NULL,
                price REAL NOT NULL,
                exchange TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                order_id TEXT,
                profit_loss REAL DEFAULT 0
            )
        ''')

        # Analysis table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_data TEXT NOT NULL,
                recommendation TEXT,
                confidence REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                message TEXT NOT NULL,
                severity TEXT DEFAULT 'info',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                acknowledged BOOLEAN DEFAULT FALSE
            )
        ''')

        self.connection.commit()
        logger.info("✅ Database tables created")

    async def save_trade(self, trade_data: Dict) -> int:
        """Save trade to database"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO trades (symbol, side, amount, price, exchange, status, order_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get('symbol'),
                trade_data.get('side'),
                trade_data.get('amount'),
                trade_data.get('price'),
                trade_data.get('exchange'),
                trade_data.get('status', 'pending'),
                trade_data.get('order_id')
            ))

            self.connection.commit()
            trade_id = cursor.lastrowid
            logger.info(f"✅ Trade saved with ID: {trade_id}")
            return trade_id

        except Exception as e:
            logger.error(f"❌ Error saving trade: {e}")
            return None

    async def save_analysis(self, symbol: str, analysis_data: Dict) -> int:
        """Save analysis to database"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO analysis (symbol, analysis_data, recommendation, confidence)
                VALUES (?, ?, ?, ?)
            ''', (
                symbol,
                json.dumps(analysis_data),
                analysis_data.get('recommendation'),
                analysis_data.get('confidence')
            ))

            self.connection.commit()
            analysis_id = cursor.lastrowid
            logger.debug(f"✅ Analysis saved for {symbol}")
            return analysis_id

        except Exception as e:
            logger.error(f"❌ Error saving analysis: {e}")
            return None

    async def get_recent_trades(self, limit: int = 10) -> List[Dict]:
        """Get recent trades"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT * FROM trades
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"❌ Error getting trades: {e}")
            return []

    async def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("✅ Database connection closed")
