#!/bin/bash
"""
Startup script voor Heartbeat Monitor
"""

echo "🚀 Starting Heartbeat Monitor..."

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "✅ Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️ No virtual environment found, using system Python"
fi

# Check if required packages are installed
python3 -c "import psutil, aiohttp, loguru" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Missing required packages. Installing..."
    pip install psutil aiohttp loguru
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start heartbeat monitor
echo "💓 Starting Heartbeat Monitor..."
python3 heartbeat_monitor.py

echo "💓 Heartbeat Monitor stopped."
