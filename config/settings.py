"""
Configuration management for the Telegram Trading Bot
Unified settings module with comprehensive validation
"""
import os
from typing import List, Optional, Dict, Any, Union, Tuple
from pathlib import Path
from dotenv import load_dotenv
from distutils.util import strtobool
import sys

# Simple logging for settings (avoid circular imports)
import logging
logger = logging.getLogger(__name__)

# Load environment variables
dotenv_path = Path(__file__).parent / ".env"
if dotenv_path.exists():
    load_dotenv(dotenv_path)
else:
    # fallback: load from project root or config
    root_env = Path(__file__).parent.parent / ".env"
    if root_env.exists():
        load_dotenv(root_env)
    else:
        load_dotenv()

def parse_bool(value: str, default: bool = False) -> bool:
    """
    Convert a string environment value to bool.
    Accepts 1/0, y/n, yes/no, true/false (case-insensitive).
    Returns *default* when conversion fails.
    """
    if not value:
        return default
    try:
        return bool(strtobool(value))
    except ValueError:
        return default

def parse_list(value: str, separator: str = ",", item_type: type = str) -> List[Any]:
    """Parse a string into a list of typed values"""
    if not value or not value.strip():
        return []

    items = [item.strip() for item in value.split(separator) if item.strip()]

    if item_type == int:
        return [int(item) for item in items if item.isdigit()]
    elif item_type == float:
        return [float(item) for item in items if item.replace(".", "", 1).isdigit()]

    return items

class Settings:
    """
    Unified application settings for the trading bot

    Deze klasse beheert alle configuratie-instellingen voor de trading bot,
    inclusief exchange credentials, trading parameters, en beveiligingsinstellingen.
    """

    def __init__(self):
        # Laad configuratie met validatie
        self._load_telegram_config()
        self._load_exchange_configs()
        self._load_trading_configs()
        self._load_security_configs()
        self._validate_critical_settings()

    def _load_telegram_config(self):
        """Laad Telegram bot configuratie"""
        self.telegram_bot_token = self._get_required_env("TELEGRAM_BOT_TOKEN")

        # Parse multiple admin user IDs (comma-separated)
        admin_ids_str = os.getenv("TELEGRAM_ADMIN_USER_ID", "")
        self.telegram_admin_user_ids = parse_list(admin_ids_str, item_type=int)

        # Keep backward compatibility
        self.telegram_admin_user_id = self.telegram_admin_user_ids[0] if self.telegram_admin_user_ids else 0

        if not self.telegram_admin_user_ids:
            raise ValueError("At least one TELEGRAM_ADMIN_USER_ID must be configured")

    def _load_exchange_configs(self):
        """Laad exchange configuraties met verbeterde beveiliging"""
        # KuCoin Configuration
        self.kucoin_api_key = self._get_secure_env("KUCOIN_API_KEY")
        self.kucoin_secret_key = self._get_secure_env("KUCOIN_SECRET_KEY")
        self.kucoin_passphrase = self._get_secure_env("KUCOIN_PASSPHRASE")
        self.kucoin_sandbox = parse_bool(os.getenv("KUCOIN_SANDBOX", "1"))  # Default to sandbox

        # MEXC Configuration
        self.mexc_api_key = self._get_secure_env("MEXC_API_KEY")
        self.mexc_secret_key = self._get_secure_env("MEXC_SECRET_KEY")
        self.mexc_sandbox = parse_bool(os.getenv("MEXC_SANDBOX", "1"))  # Default to sandbox

        # Binance Configuration
        self.binance_api_key = self._get_secure_env("BINANCE_API_KEY")
        self.binance_api_secret = self._get_secure_env("BINANCE_API_SECRET")
        self.binance_sandbox = parse_bool(os.getenv("BINANCE_SANDBOX", "1"))  # Default to sandbox

        # Generic Exchange selections
        self.exchange = os.getenv("EXCHANGE", "kucoin").lower()
        self.use_testnet = parse_bool(os.getenv("USE_TESTNET", "1"))  # Default to testnet
        self.live_mode = parse_bool(os.getenv("LIVE_MODE", "0"))

        # Ensure mutual exclusivity en veiligheid
        if self.live_mode and self.use_testnet:
            raise ValueError("LIVE_MODE and USE_TESTNET cannot both be True")

        # Waarschuwing voor live mode
        if self.live_mode:
            logger.warning("🚨 LIVE MODE ENABLED - Real money trading active!")

    def _load_trading_configs(self):
        """Laad trading configuratie parameters"""
        # Trading Settings
        self.trading_interval = int(os.getenv("TRADING_INTERVAL", "60"))  # seconds
        self.error_retry_delay = int(os.getenv("ERROR_RETRY_DELAY", "30"))  # seconds
        self.max_trades_per_day = int(os.getenv("MAX_TRADES_PER_DAY", "10"))

        # Analysis Settings
        self.sentiment_analysis_enabled = parse_bool(os.getenv("SENTIMENT_ANALYSIS_ENABLED", "true"))
        self.sentiment_weight = float(os.getenv("SENTIMENT_WEIGHT", "0.3"))
        self.advanced_indicators_enabled = parse_bool(os.getenv("ADVANCED_INDICATORS_ENABLED", "true"))
        self.custom_indicators = parse_list(os.getenv("CUSTOM_INDICATORS", "RSI,MACD,Bollinger"))
        self.indicator_timeframes = parse_list(os.getenv("INDICATOR_TIMEFRAMES", "1h,4h,1d"))

        # Risk Management Settings
        self.risk_per_trade = float(os.getenv("RISK_PER_TRADE", "0.02"))  # 2% risk per trade
        self.max_position_size = float(os.getenv("MAX_POSITION_SIZE", "0.1"))  # 10% of portfolio
        self.stop_loss_percent = float(os.getenv("STOP_LOSS_PERCENT", "0.05"))  # 5% stop loss
        self.take_profit_percent = float(os.getenv("TAKE_PROFIT_PERCENT", "0.15"))  # 15% take profit
        self.max_drawdown = float(os.getenv("MAX_DRAWDOWN", "0.20"))  # 20% max drawdown
        self.max_daily_trades = int(os.getenv("MAX_DAILY_TRADES", "10"))
        self.max_open_positions = int(os.getenv("MAX_OPEN_POSITIONS", "5"))
        self.position_sizing_method = os.getenv("POSITION_SIZING_METHOD", "risk_based")

        # Trading Pairs Settings
        self.trading_pairs = parse_list(os.getenv("TRADING_PAIRS", "BTC/USDT,ETH/USDT"))
        self.min_volume_24h = float(os.getenv("MIN_VOLUME_24H", "1000000"))  # Minimum 24h volume in USDT

    def _load_security_configs(self):
        """Laad beveiligings configuratie"""
        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///trading_bot.db")

        # Security Settings
        self.api_rate_limit = int(os.getenv("API_RATE_LIMIT", "10"))  # requests per second
        self.max_login_attempts = int(os.getenv("MAX_LOGIN_ATTEMPTS", "3"))
        self.session_timeout = int(os.getenv("SESSION_TIMEOUT", "3600"))  # 1 hour
        self.enable_2fa = parse_bool(os.getenv("ENABLE_2FA", "false"))

        # Encryption settings
        self.encryption_key = os.getenv("ENCRYPTION_KEY", "")
        if not self.encryption_key and self.live_mode:
            logger.warning("⚠️ No encryption key set for live mode!")

    def _get_required_env(self, key: str) -> str:
        """Haal vereiste environment variable op"""
        value = os.getenv(key, "")
        if not value:
            raise ValueError(f"Required environment variable {key} is not set")
        return value

    def _get_secure_env(self, key: str) -> str:
        """Haal beveiligde environment variable op met logging"""
        value = os.getenv(key, "")
        if value:
            # Log dat de key is geladen (maar niet de waarde)
            logger.debug(f"✅ Loaded {key} configuration")
        else:
            logger.warning(f"⚠️ {key} not configured")
        return value

    def _validate_critical_settings(self):
        """Valideer kritieke instellingen"""
        errors = []

        # Valideer Telegram configuratie
        if not self.telegram_bot_token:
            errors.append("TELEGRAM_BOT_TOKEN is required")

        # Valideer dat minstens één exchange is geconfigureerd
        exchanges_configured = any([
            self.kucoin_api_key and self.kucoin_secret_key,
            self.mexc_api_key and self.mexc_secret_key,
            self.binance_api_key and self.binance_api_secret
        ])

        if not exchanges_configured:
            errors.append("At least one exchange must be configured")

        # Valideer risk management parameters
        if self.risk_per_trade <= 0 or self.risk_per_trade > 0.1:
            errors.append("RISK_PER_TRADE must be between 0 and 0.1 (10%)")

        if self.max_drawdown <= 0 or self.max_drawdown > 0.5:
            errors.append("MAX_DRAWDOWN must be between 0 and 0.5 (50%)")

        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")

        logger.info("✅ Configuration validation passed")

        # Logging Configuration
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_file = os.getenv("LOG_FILE", "trading_bot.log")

        # Twitter API Credentials (optionally include if still needed)
        self.twitter_bearer_token = os.getenv("TWITTER_BEARER_TOKEN", "")
        self.twitter_api_key = os.getenv("TWITTER_API_KEY", "")
        self.twitter_api_secret = os.getenv("TWITTER_API_SECRET", "")
        self.twitter_access_token = os.getenv("TWITTER_ACCESS_TOKEN", "")
        self.twitter_access_token_secret = os.getenv("TWITTER_ACCESS_TOKEN_SECRET", "")

        # Advanced Risk Settings
        self.max_daily_loss = float(os.getenv("MAX_DAILY_LOSS", "0.05"))  # 5% max daily loss
        self.trailing_stop_enabled = parse_bool(os.getenv("TRAILING_STOP_ENABLED", "false"))
        self.trailing_stop_percent = float(os.getenv("TRAILING_STOP_PERCENT", "0.02"))  # 2% trailing stop
        self.position_scaling_enabled = parse_bool(os.getenv("POSITION_SCALING_ENABLED", "false"))
        self.scale_in_levels = parse_list(os.getenv("SCALE_IN_LEVELS", "0.01,0.02,0.03"), item_type=float)
        self.scale_out_levels = parse_list(os.getenv("SCALE_OUT_LEVELS", "0.02,0.03,0.05"), item_type=float)

    def validate(self) -> Tuple[bool, List[str]]:
        """Validate required settings and return status with any missing fields"""

        # Define required fields with their values
        required_fields = [
            ("telegram_bot_token", self.telegram_bot_token),
            ("telegram_admin_user_ids", self.telegram_admin_user_ids),
        ]

        # Add exchange-specific required fields based on selected exchange
        if self.exchange == "kucoin":
            required_fields.extend([
                ("kucoin_api_key", self.kucoin_api_key),
                ("kucoin_secret_key", self.kucoin_secret_key),
                ("kucoin_passphrase", self.kucoin_passphrase),
            ])
        elif self.exchange == "mexc":
            required_fields.extend([
                ("mexc_api_key", self.mexc_api_key),
                ("mexc_secret_key", self.mexc_secret_key),
            ])
        elif self.exchange == "binance":
            required_fields.extend([
                ("binance_api_key", self.binance_api_key),
                ("binance_api_secret", self.binance_api_secret),
            ])

        # Always require encryption key for security
        required_fields.append(("encryption_key", self.encryption_key))

        # Identify missing fields
        missing_fields = []
        for field_name, field_value in required_fields:
            if not field_value or (isinstance(field_value, int) and field_value == 0) or \
               (isinstance(field_value, list) and not field_value):
                missing_fields.append(field_name)

        is_valid = len(missing_fields) == 0
        return is_valid, missing_fields

    def is_authorized_user(self, user_id: int) -> bool:
        """Check if user ID is authorized"""
        return user_id in self.telegram_admin_user_ids

    def __str__(self) -> str:
        """Return a printable representation with sensitive info masked"""
        sensitive_fields = ["api_key", "secret", "token", "passphrase", "encryption_key"]
        result = []

        for key, value in self.__dict__.items():
            if any(sensitive in key.lower() for sensitive in sensitive_fields):
                masked_value = f"{'*' * 8}...{str(value)[-4:]}" if value else "[NOT SET]"
                result.append(f"{key}: {masked_value}")
            else:
                result.append(f"{key}: {value}")

        return "\n".join(result)


# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get application settings"""
    return settings
