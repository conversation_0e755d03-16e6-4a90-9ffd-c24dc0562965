version: 1.0.0
name: continue-config
schema: v1
models:
  - name: Llama 3.1 8B
    provider: ollama
    model: llama3.1:8b
    roles:
      - chat
      - edit
      - apply
  - name: Qwen2.5-Coder 1.5B
    provider: ollama
    model: qwen2.5-coder:1.5b-base
    roles:
      - autocomplete
  - name: Nomic Embed
    provider: ollama
    model: nomic-embed-text:latest
    roles:
      - embed
  - name: WizardCoder 34B (Architect)
    provider: openai
    apiBase: http://localhost:1234/v1
    apiKey: lm-studio
    model: wizardcoder-python-34b-v1.0
    systemPrompt: Je bent een behulpzame Python-expert die complexe trading-bots
      bouwt en optimaliseert. Je focus ligt op algoritmische handel,
      fouttolerante architectuur en best-practice Python-coding-standaarden.
      Schrijf du<PERSON>li<PERSON>e, production-ready code met uitgebreide
      inline-documentatie en logging.
    temperature: 0.2
    contextLength: 8192
    priority: 1
    roles:
      - chat
      - edit
      - apply
  - name: DeepSeek Coder (Optimizer)
    provider: openai
    apiBase: http://localhost:1234/v1
    apiKey: lm-studio
    model: deepseek-coder-v2-lite-instruct-mlx
    systemPrompt:
      Je bent een expert in het optimaliseren van trading algoritmes en
      high-performance code. Je analyseert bestaande trading bot code en
      identificeert performance bottlenecks. Je verbetert algoritmische
      efficiëntie, geheugengebruik en latentie-kritische componenten voor
      real-time trading.
    temperature: 0.3
    contextLength: 4096
    priority: 2
    roles:
      - chat
      - edit
      - apply
  - name: CodeLlama 7B (Implementer)
    provider: openai
    apiBase: http://localhost:1234/v1
    apiKey: lm-studio
    model: codellama-7b-instruct-hf-mlx-2
    systemPrompt:
      Je bent een snelle, lichtgewicht assistent voor kleine scripts en
      snelle iteraties. Richt je op het beantwoorden van concrete Python-vragen
      en het snel aanpassen van bestaande trading-bot-fragmenten. Lever
      beknopte, goed uitlegbare voorbeelden met minimale afhankelijkheden.
    temperature: 0.3
    contextLength: 4096
    priority: 3
    roles:
      - chat
      - edit
      - apply
  - name: Phi-3 Mini (Debugger)
    provider: openai
    apiBase: http://localhost:1234/v1
    apiKey: lm-studio
    model: phi-3-mini-4k-instruct
    systemPrompt:
      Je bent een ultralicht maar slim hulpje voor snelle verificaties,
      debugging-tips en korte code-snippets. Geef heldere, directe antwoorden en
      beperk je tot de essentie. Perfect voor snelle checks van parameters,
      foutmeldingen of mini-scripts.
    temperature: 0.2
    contextLength: 2048
    priority: 4
    roles:
      - chat
      - edit
      - apply
defaultModel: wizardcoder-python-34b-v1.0
autoSwitchingModels:
  enabled: true
  rules:
    - pattern: debug|fout|error|fix|test|unittest
      model: phi-3-mini-4k-instruct
    - pattern: optimize|performance|speed|efficiënt|sneller
      model: deepseek-coder-v2-lite-instruct-mlx
    - pattern: implementeer|aanpassen|wijzigen|update|kleine|update
      model: codellama-7b-instruct-hf-mlx-2
    - pattern: ontwerp|architectuur|strategie|complex|structuur
      model: wizardcoder-python-34b-v1.0
collaborativeMode:
  enabled: true
  patterns:
    - trigger: samenwerking|samen|collaborative
      models:
        - wizardcoder-python-34b-v1.0
        - deepseek-coder-v2-lite-instruct-mlx
    - trigger: debug en optimaliseer
      models:
        - phi-3-mini-4k-instruct
        - deepseek-coder-v2-lite-instruct-mlx
    - trigger: verbeter trading bot
      models:
        - wizardcoder-python-34b-v1.0
        - deepseek-coder-v2-lite-instruct-mlx
        - phi-3-mini-4k-instruct
    - trigger: kleine wijziging testen
      models:
        - codellama-7b-instruct-hf-mlx-2
        - phi-3-mini-4k-instruct
teamMode:
  enabled: true
  defaultTeam:
    - wizardcoder-python-34b-v1.0
    - deepseek-coder-v2-lite-instruct-mlx
  teams:
    architecture:
      - wizardcoder-python-34b-v1.0
    optimization:
      - deepseek-coder-v2-lite-instruct-mlx
    implementation:
      - codellama-7b-instruct-hf-mlx-2
    testing:
      - phi-3-mini-4k-instruct
    fullstack:
      - wizardcoder-python-34b-v1.0
      - deepseek-coder-v2-lite-instruct-mlx
      - phi-3-mini-4k-instruct
    quickfix:
      - codellama-7b-instruct-hf-mlx-2
      - phi-3-mini-4k-instruct
codeModifications:
  format: >
    ```language /path/to/file

    // ... existing code ...


    {{ modified code here }}


    // ... existing code ...


    {{ another modification }}


    // ... rest of code ...

    ```


    In existing files, you should always restate the function or class that the
    snippet belongs to:


    ```language /path/to/file

    // ... existing code ...


    function exampleFunction() {
      // ... existing code ...
    ```
context:
  - provider: code
  - provider: docs
  - provider: diff
  - provider: terminal
  - provider: problems
  - provider: folder
  - provider: codebase
