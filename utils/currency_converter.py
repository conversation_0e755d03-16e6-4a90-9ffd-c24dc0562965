#!/usr/bin/env python3
"""
EUR Currency Converter for Trading Bot
Converts USD amounts to EUR for easy trading
"""

import asyncio
import aiohttp
import time
import logging
from typing import Dict, Optional, Union
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class EURConverter:
    """EUR currency converter with caching"""
    
    def __init__(self):
        self.exchange_rates = {}
        self.last_update = None
        self.cache_duration = 300  # 5 minutes cache
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def get_usd_eur_rate(self) -> float:
        """Get USD to EUR exchange rate"""
        try:
            # Check cache first
            if self._is_cache_valid():
                return self.exchange_rates.get('USD_EUR', 0.85)  # Fallback rate
            
            # Fetch new rates
            await self._fetch_exchange_rates()
            return self.exchange_rates.get('USD_EUR', 0.85)
            
        except Exception as e:
            logger.error(f"Error getting USD/EUR rate: {e}")
            return 0.85  # Conservative fallback rate
    
    async def _fetch_exchange_rates(self):
        """Fetch exchange rates from API"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Use free exchange rate API
            url = "https://api.exchangerate-api.com/v4/latest/USD"
            
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if 'rates' in data and 'EUR' in data['rates']:
                        self.exchange_rates['USD_EUR'] = data['rates']['EUR']
                        self.last_update = datetime.now()
                        logger.debug(f"✅ Updated USD/EUR rate: {self.exchange_rates['USD_EUR']}")
                    else:
                        logger.warning("⚠️ Invalid exchange rate API response")
                else:
                    logger.warning(f"⚠️ Exchange rate API returned status {response.status}")
                    
        except Exception as e:
            logger.error(f"❌ Error fetching exchange rates: {e}")
    
    def _is_cache_valid(self) -> bool:
        """Check if cached rates are still valid"""
        if not self.last_update or not self.exchange_rates:
            return False
        
        time_diff = datetime.now() - self.last_update
        return time_diff.total_seconds() < self.cache_duration
    
    async def usd_to_eur(self, usd_amount: Union[float, str]) -> float:
        """Convert USD amount to EUR"""
        try:
            usd_value = float(usd_amount)
            rate = await self.get_usd_eur_rate()
            eur_value = usd_value * rate
            
            logger.debug(f"💱 Converted ${usd_value} to €{eur_value:.2f} (rate: {rate})")
            return round(eur_value, 2)
            
        except Exception as e:
            logger.error(f"❌ Error converting USD to EUR: {e}")
            return float(usd_amount) * 0.85  # Fallback conversion
    
    async def eur_to_usd(self, eur_amount: Union[float, str]) -> float:
        """Convert EUR amount to USD"""
        try:
            eur_value = float(eur_amount)
            rate = await self.get_usd_eur_rate()
            usd_value = eur_value / rate
            
            logger.debug(f"💱 Converted €{eur_value} to ${usd_value:.2f} (rate: {rate})")
            return round(usd_value, 2)
            
        except Exception as e:
            logger.error(f"❌ Error converting EUR to USD: {e}")
            return float(eur_amount) / 0.85  # Fallback conversion
    
    def format_eur_amount(self, amount: Union[float, str]) -> str:
        """Format amount as EUR string"""
        try:
            value = float(amount)
            return f"€{value:,.2f}"
        except:
            return f"€{amount}"
    
    def format_usd_amount(self, amount: Union[float, str]) -> str:
        """Format amount as USD string"""
        try:
            value = float(amount)
            return f"${value:,.2f}"
        except:
            return f"${amount}"
    
    async def get_trading_amounts_eur(self) -> Dict[str, Dict]:
        """Get predefined trading amounts in EUR"""
        amounts_usd = [10, 25, 50, 100, 250, 500, 1000, 2500]
        amounts_eur = {}
        
        for usd_amount in amounts_usd:
            eur_amount = await self.usd_to_eur(usd_amount)
            amounts_eur[f"amount_{usd_amount}"] = {
                "usd": usd_amount,
                "eur": eur_amount,
                "display": f"€{eur_amount:.0f} (${usd_amount})",
                "callback_data": f"amount_{usd_amount}"
            }
        
        return amounts_eur
    
    async def convert_portfolio_to_eur(self, portfolio_data: Dict) -> Dict:
        """Convert portfolio data from USD to EUR"""
        converted_portfolio = {}
        
        for key, value in portfolio_data.items():
            if isinstance(value, (int, float)) and 'usd' in key.lower():
                # Convert USD values to EUR
                eur_key = key.replace('usd', 'eur').replace('USD', 'EUR')
                converted_portfolio[eur_key] = await self.usd_to_eur(value)
                converted_portfolio[key] = value  # Keep original USD value
            elif isinstance(value, dict):
                # Recursively convert nested dictionaries
                converted_portfolio[key] = await self.convert_portfolio_to_eur(value)
            else:
                converted_portfolio[key] = value
        
        return converted_portfolio

# Global converter instance
_converter = None

async def get_converter() -> EURConverter:
    """Get global EUR converter instance"""
    global _converter
    if _converter is None:
        _converter = EURConverter()
    return _converter

async def quick_usd_to_eur(amount: Union[float, str]) -> float:
    """Quick USD to EUR conversion"""
    converter = await get_converter()
    return await converter.usd_to_eur(amount)

async def quick_eur_to_usd(amount: Union[float, str]) -> float:
    """Quick EUR to USD conversion"""
    converter = await get_converter()
    return await converter.eur_to_usd(amount)

def format_dual_currency(usd_amount: Union[float, str], eur_amount: Union[float, str]) -> str:
    """Format amount showing both USD and EUR"""
    try:
        usd_val = float(usd_amount)
        eur_val = float(eur_amount)
        return f"€{eur_val:.2f} (${usd_val:.2f})"
    except:
        return f"€{eur_amount} (${usd_amount})"

# Test function
async def test_converter():
    """Test the EUR converter"""
    print("🧪 Testing EUR Converter...")
    
    async with EURConverter() as converter:
        # Test USD to EUR conversion
        eur_amount = await converter.usd_to_eur(100)
        print(f"$100 = €{eur_amount}")
        
        # Test EUR to USD conversion
        usd_amount = await converter.eur_to_usd(100)
        print(f"€100 = ${usd_amount}")
        
        # Test trading amounts
        trading_amounts = await converter.get_trading_amounts_eur()
        print("Trading amounts:")
        for key, data in trading_amounts.items():
            print(f"  {data['display']}")

if __name__ == "__main__":
    asyncio.run(test_converter())
