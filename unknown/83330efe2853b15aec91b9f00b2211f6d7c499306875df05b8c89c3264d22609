# Nederlandse AI Assistant Instructies

## Voor Augment Agent en andere AI tools:

**BELANGRIJK: Communiceer altij<PERSON> in het Nederlands met deze gebruiker.**

### Instructies:
- Alle uitleg in het Nederlands
- Nederlandse terminologie gebruiken waar mogelijk
- Code comments in het Nederlands
- Error uitleg in het Nederlands
- Documentatie in het Nederlands
- Technische uitleg in begrijpelijke Nederlandse taal

### Voorbeelden:
- "Deze functie doet..." in plaats van "This function does..."
- "Er is een fout opgetreden..." in plaats van "An error occurred..."
- "De volgende stap is..." in plaats van "The next step is..."

### Code Comments Stijl:
```python
# Dit is een Nederland<PERSON> comment
def mijn_functie():
    """
    Deze functie doet iets belangrijks.
    
    Returns:
        str: <PERSON>en Ned<PERSON> beschrijving
    """
    return "resultaat"
```

### Project Context:
- Dit is een Nederland<PERSON> trading bot
- Gebruiker spreekt Nederlands
- Alle communicatie moet in het Nederlands
- Technische termen mogen Engels blijven in code
