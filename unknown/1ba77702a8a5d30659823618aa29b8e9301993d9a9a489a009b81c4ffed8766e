#!/bin/bash
# Quick fix voor de trading bot

echo "🔧 Quick Fix voor MyOwnMoneyMaker Trading Bot"
echo "============================================"

# Stop de bot
./stop-24-7.sh 2>/dev/null || true

# Uninstall conflicting packages uit global environment
echo "📦 Cleaning up global packages..."
pip uninstall -y httpx httpx-aiohttp python-telegram-bot 2>/dev/null || true

# Install juiste versies globally (tijdelijke oplossing)
echo "📦 Installing correct versions..."
pip install httpx==0.26.0
pip install python-telegram-bot==20.8
pip install ccxt==4.4.76
pip install pandas pandas_ta loguru aiohttp python-dotenv

# Start de bot met originele script
echo "🚀 Starting bot..."
./start-24-7.sh

echo ""
echo "✅ Quick fix applied!"
echo ""
echo "⚠️  BELANGRIJK:"
echo "- <PERSON><PERSON> draait nu in LIVE mode (geen sandbox)"
echo "- <PERSON><PERSON> met ZEER kleine bedragen!"
echo "- Monitor de bot via Telegram"
echo "- Check logs met: tail -f logs/bot.log"
