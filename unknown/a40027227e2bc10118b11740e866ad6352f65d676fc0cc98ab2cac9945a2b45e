# MyOwnMoneyMaker Trading Bot - 24/7 Setup

## 🚀 24/7 Trading Bot Setup

Je trading bot kan nu 24/7 dra<PERSON><PERSON> zonder dat je computer aan hoeft te blijven staan. Er zijn verschillende opties beschikbaar:

### 📋 Beschikbare Scripts

#### 🟢 Eenvoudige 24/7 Setup (Aanbevolen)
```bash
./start-24-7.sh      # Start de bot in de achtergrond
./stop-24-7.sh       # Stop de bot
./restart-24-7.sh    # Herstart de bot
./status-24-7.sh     # Controleer bot status
./monitor-24-7.sh    # Live monitoring dashboard
```

#### 🐳 Docker Setup (Voor gevorderden)
```bash
./start-docker-simple.sh    # Start bot in Docker container
./stop-docker-simple.sh     # Stop Docker container
```

#### 🔧 Docker Compose Setup (Volledig)
```bash
./start-docker.sh    # Start met Docker Compose
./stop-docker.sh     # Stop Docker Compose
```

## 🎯 Snelle Start

### 1. Start de Bot 24/7
```bash
cd /Users/<USER>/Desktop/Myownmoneymaker
./start-24-7.sh
```

### 2. Controleer Status
```bash
./status-24-7.sh
```

### 3. Monitor Live
```bash
./monitor-24-7.sh
```

## 📊 Bot Status Controleren

### Handmatige Controle
```bash
# Controleer of bot draait
ps aux | grep telegram_simple

# Bekijk logs
tail -f logs/bot.log

# Bekijk recente logs
tail -50 logs/bot.log
```

### Automatische Monitoring
Het `monitor-24-7.sh` script biedt:
- ✅ Real-time status monitoring
- 🔄 Automatische herstart bij crashes
- 📊 Memory en CPU usage
- 📝 Live log viewing
- 🎛️ Interactieve controls

## 🔧 Troubleshooting

### Bot Start Niet
```bash
# Controleer dependencies
python3 -m pip install -r requirements.txt

# Controleer .env file
cat .env

# Bekijk error logs
cat logs/bot.log
```

### Bot Crashed
```bash
# Herstart automatisch
./restart-24-7.sh

# Of handmatig
./stop-24-7.sh
./start-24-7.sh
```

### Memory Issues
```bash
# Controleer memory usage
./status-24-7.sh

# Herstart als nodig
./restart-24-7.sh
```

## 📁 Log Files

- `logs/bot.log` - Hoofdlog bestand
- `logs/` - Alle log bestanden

## 🔒 Beveiliging

### Belangrijke Tips:
1. **Houd je .env file veilig** - Deel nooit je API keys
2. **Monitor regelmatig** - Gebruik `./monitor-24-7.sh`
3. **Backup je configuratie** - Kopieer .env naar veilige locatie
4. **Controleer trades** - Bekijk regelmatig je Telegram bot

## 🌐 Remote Access

### SSH Access (Voor VPS/Server)
```bash
# Verbind met je server
ssh <EMAIL>

# Ga naar bot directory
cd /path/to/Myownmoneymaker

# Start monitoring
./monitor-24-7.sh
```

### Screen/Tmux (Voor persistente sessies)
```bash
# Start screen sessie
screen -S trading-bot

# Start monitor
./monitor-24-7.sh

# Detach: Ctrl+A, D
# Reattach: screen -r trading-bot
```

## 📈 Performance Monitoring

### System Resources
```bash
# CPU en Memory
top -p $(pgrep -f telegram_simple)

# Disk usage
df -h

# Network connections
netstat -an | grep python
```

### Bot Metrics
- Bekijk Telegram bot voor trading statistieken
- Controleer exchange balances
- Monitor trade history

## 🚨 Alerts & Notifications

De bot stuurt automatisch meldingen naar Telegram bij:
- ✅ Succesvolle trades
- ❌ Trading errors
- 📊 Market alerts
- 💰 Portfolio updates

## 🔄 Auto-Restart Setup

Voor maximale uptime kun je een cron job instellen:

```bash
# Edit crontab
crontab -e

# Voeg toe (controleert elke 5 minuten):
*/5 * * * * cd /Users/<USER>/Desktop/Myownmoneymaker && ./status-24-7.sh > /dev/null 2>&1 || ./start-24-7.sh
```

## 📞 Support

Bij problemen:
1. Controleer `logs/bot.log`
2. Run `./status-24-7.sh`
3. Herstart met `./restart-24-7.sh`
4. Bekijk Telegram bot voor errors

---

**🎉 Je trading bot draait nu 24/7 en handelt automatisch!**
