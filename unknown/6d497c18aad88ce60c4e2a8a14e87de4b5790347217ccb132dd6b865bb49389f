#!/bin/bash

# VSCode Nederlands Setup Script
echo "🇳🇱 VSCode Nederlands Configuratie Setup..."

# Backup bestaande settings
VSCODE_SETTINGS="$HOME/Library/Application Support/Code/User/settings.json"
if [ -f "$VSCODE_SETTINGS" ]; then
    echo "📋 Backup maken van bestaande settings..."
    cp "$VSCODE_SETTINGS" "$VSCODE_SETTINGS.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Maak settings directory als het niet bestaat
mkdir -p "$HOME/Library/Application Support/Code/User"

# Schrijf Nederlandse instellingen
cat > "$VSCODE_SETTINGS" << 'EOF'
{
    // Basis taal instellingen
    "locale": "nl",
    "workbench.language": "nl",
    
    // AI en Augment instellingen
    "augment.language": "nederlands",
    "augment.responseLanguage": "dutch",
    "augment.communicationLanguage": "nl",
    "augment.locale": "nl-NL",
    "augment.projectLanguage": "nederlands",
    
    // GitHub Copilot Nederlands
    "github.copilot.advanced": {
        "language": "nl",
        "locale": "nl-NL",
        "responseLanguage": "dutch",
        "communicationLanguage": "nederlands"
    },
    
    // AI Assistant instellingen
    "ai.language": "nl",
    "ai.responseLanguage": "nederlands",
    "ai.communicationLanguage": "dutch",
    "ai.locale": "nl-NL",
    
    // Editor instellingen
    "editor.fontSize": 14,
    "editor.fontFamily": "SF Mono, Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace",
    "editor.formatOnSave": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    
    // Workbench instellingen
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "vs-seti",
    
    // Terminal instellingen
    "terminal.integrated.defaultProfile.osx": "bash",
    "terminal.integrated.fontSize": 13,
    
    // Bestand instellingen
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    
    // Python instellingen
    "python.defaultInterpreterPath": "python3",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    
    // Extensie instellingen
    "extensions.autoUpdate": true,
    "extensions.autoCheckUpdates": true,
    
    // Git instellingen
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    
    // Zoek instellingen
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/logs": true,
        "**/__pycache__": true,
        "**/.git": true
    }
}
EOF

echo "✅ VSCode Nederlandse instellingen toegepast!"
echo ""
echo "📋 **Wat is ingesteld:**"
echo "   - Augment Agent spreekt Nederlands"
echo "   - GitHub Copilot in het Nederlands"
echo "   - AI Assistenten communiceren in Nederlands"
echo "   - Alle responses in het Nederlands"
echo ""
echo "🔄 **Herstart VSCode om wijzigingen toe te passen**"
echo ""
echo "🎯 **Test het door:**"
echo "   - Augment Agent te vragen iets uit te leggen"
echo "   - GitHub Copilot te gebruiken"
echo "   - AI assistenten te gebruiken"
echo ""
echo "✅ **Klaar! VSCode is nu geconfigureerd voor Nederlands!**"
