import asyncio

from loguru import logger

from exchanges.kucoin import KuCoinExchange
from exchanges.mexc import MEXCExchange


async def test_kucoin_connection():
    logger.info("Testing KuCoin connection...")
    kucoin = KuCoinExchange(
        api_key="your_api_key", secret_key="your_secret_key", passphrase="your_passphrase", sandbox=False
    )
    connected = await kucoin.connect()
    logger.info(f"KuCoin connection status: {'Success' if connected else 'Failed'}")


async def test_mexc_connection():
    logger.info("Testing MEXC connection...")
    mexc = MEXCExchange(api_key="your_api_key", secret_key="your_secret_key", sandbox=False)
    connected = await mexc.connect()
    logger.info(f"MEXC connection status: {'Success' if connected else 'Failed'}")


async def main():
    await test_kucoin_connection()
    await test_mexc_connection()


if __name__ == "__main__":
    asyncio.run(main())
