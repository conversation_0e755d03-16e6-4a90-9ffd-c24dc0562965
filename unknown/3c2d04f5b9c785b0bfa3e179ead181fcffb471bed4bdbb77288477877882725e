#!/usr/bin/env python3
"""
Quick configuration test for the trading bot
"""
import asyncio
import sys
sys.path.insert(0, '.')

from core.config_checker import check_configuration

async def main():
    """Run configuration check"""
    print("🔍 Checking Trading Bot Configuration...\n")
    
    report = await check_configuration()
    print(report)
    
    print("\n✅ Configuration check complete!")

if __name__ == "__main__":
    asyncio.run(main())
