#!/usr/bin/env python3
"""
Advanced Cryptocurrency Trading Bot
Main entry point for the trading bot
"""

import asyncio
import logging
import os
import signal
import sys

from dotenv import load_dotenv

# Import bot modules
from bot.telegram_bot import TelegramBot
from exchanges.manager import ExchangeManager
from analysis.market_analyzer import MarketAnalyzer
from trading.trade_executor import TradeExecutor
from database.db_manager import DatabaseManager
from utils.logger import setup_logger

# Load environment variables
load_dotenv()


class TradingBot:
    """Main trading bot class"""

    def __init__(self):
        self.logger = setup_logger(__name__)
        self.running = False

        # Initialize components
        self.db_manager = None
        self.exchange_manager = None
        self.market_analyzer = None
        self.trade_executor = None
        self.telegram_bot = None

    async def initialize(self):
        """Initialize all bot components"""
        try:
            self.logger.info("🚀 Initializing Trading Bot...")

            # Initialize database
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            self.logger.info("✅ Database initialized")

            # Initialize exchange manager
            self.exchange_manager = ExchangeManager()
            await self.exchange_manager.initialize()
            self.logger.info("✅ Exchange manager initialized")

            # Initialize market analyzer
            self.market_analyzer = MarketAnalyzer(self.exchange_manager)
            self.logger.info("✅ Market analyzer initialized")

            # Initialize trade executor
            self.trade_executor = TradeExecutor(self.exchange_manager, self.market_analyzer, self.db_manager)
            self.logger.info("✅ Trade executor initialized")

            # Initialize Telegram bot
            self.telegram_bot = TelegramBot(
                self.exchange_manager, self.market_analyzer, self.trade_executor, self.db_manager
            )
            self.logger.info("✅ Telegram bot initialized")

            self.logger.info("🎉 All components initialized successfully!")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize bot: {e}")
            return False

    async def start(self):
        """Start the trading bot"""
        try:
            if not await self.initialize():
                self.logger.error("❌ Bot initialization failed")
                return

            self.running = True
            self.logger.info("🚀 Starting Trading Bot...")

            # Start all components
            tasks = []

            # Start Telegram bot
            if self.telegram_bot:
                tasks.append(asyncio.create_task(self.telegram_bot.start()))
                self.logger.info("✅ Telegram bot started")

            # Start market analysis
            if self.market_analyzer:
                tasks.append(asyncio.create_task(self.market_analyzer.start_analysis()))
                self.logger.info("✅ Market analysis started")

            # Start trade execution monitoring
            if self.trade_executor:
                tasks.append(asyncio.create_task(self.trade_executor.start_monitoring()))
                self.logger.info("✅ Trade executor started")

            self.logger.info("🎯 Trading Bot is now running!")
            self.logger.info("📱 Send /start to your Telegram bot to begin trading")

            # Wait for all tasks
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            else:
                # Fallback: keep running
                while self.running:
                    await asyncio.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("🛑 Received shutdown signal")
        except Exception as e:
            self.logger.error(f"❌ Bot error: {e}")
        finally:
            await self.shutdown()

    async def shutdown(self):
        """Gracefully shutdown the bot"""
        try:
            self.logger.info("🛑 Shutting down Trading Bot...")
            self.running = False

            # Stop all components
            if self.telegram_bot:
                await self.telegram_bot.stop()
                self.logger.info("✅ Telegram bot stopped")

            if self.market_analyzer:
                await self.market_analyzer.stop()
                self.logger.info("✅ Market analyzer stopped")

            if self.trade_executor:
                await self.trade_executor.stop()
                self.logger.info("✅ Trade executor stopped")

            if self.exchange_manager:
                await self.exchange_manager.close_all()
                self.logger.info("✅ Exchange connections closed")

            if self.db_manager:
                await self.db_manager.close()
                self.logger.info("✅ Database closed")

            self.logger.info("👋 Trading Bot shutdown complete")

        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")


def setup_signal_handlers(bot):
    """Setup signal handlers for graceful shutdown"""

    def signal_handler(signum, frame):
        print(f"\n🛑 Received signal {signum}")
        asyncio.create_task(bot.shutdown())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main entry point"""
    print("🤖 Advanced Cryptocurrency Trading Bot")
    print("=" * 50)

    # Check environment variables
    required_vars = [
        "TELEGRAM_BOT_TOKEN",
        "TELEGRAM_ADMIN_USER_ID",
        "KUCOIN_API_KEY",
        "KUCOIN_SECRET_KEY",
        "KUCOIN_PASSPHRASE",
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
        return

    # Create and start bot
    bot = TradingBot()
    setup_signal_handlers(bot)

    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Bot crashed: {e}")
        logging.exception("Bot crash details:")


if __name__ == "__main__":
    # Run the bot
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

# Ensure correct import of ExchangeManager
