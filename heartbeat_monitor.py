#!/usr/bin/env python3
"""
Heartbeat Monitor voor Trading Bot
Monitort de status van main.py en stuurt regelmatig updates naar Telegram admins
"""
import asyncio
import psutil
import sys
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import Settings
from bot.notify import TelegramNotifier
from core.trade_manager import get_trade_manager
from core.dashboard import get_dashboard

class HeartbeatMonitor:
    """
    Heartbeat Monitor voor het monitoren van de trading bot status

    Deze klasse checkt elke 60 seconden of main.py nog draait,
    haalt trade informatie op, en stuurt status updates naar Telegram admins.
    """

    def __init__(self):
        self.settings = Settings()
        self.notifier = TelegramNotifier(self.settings)
        self.running = False
        self.start_time = datetime.now()
        self.last_heartbeat = None
        self.heartbeat_interval = 60  # seconds
        self.main_process = None

        # Monitoring data
        self.bot_status = "Unknown"
        self.active_strategies = []
        self.system_metrics = {}

        logger.info("💓 Heartbeat Monitor initialized")

    async def initialize(self) -> bool:
        """Initialize de monitor"""
        try:
            # Initialize notifier
            if not await self.notifier.initialize():
                logger.error("❌ Failed to initialize Telegram notifier")
                return False

            # Find main.py process
            await self._find_main_process()

            logger.info("✅ Heartbeat Monitor ready")
            return True

        except Exception as e:
            logger.error(f"❌ Error initializing monitor: {e}")
            return False

    async def _find_main_process(self) -> bool:
        """Zoek het trading bot proces met verbeterde detectie"""
        try:
            # Zoek naar verschillende mogelijke bot bestanden met meer specifieke patronen
            bot_patterns = [
                'telegram_simple.py',
                'main.py',
                'bot.py',
                'trading_bot.py',
                'python telegram_simple.py',
                'python3 telegram_simple.py'
            ]

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        cmdline_str = ' '.join(cmdline).lower()

                        # Check voor Python processen die onze bot bestanden draaien
                        if 'python' in cmdline_str:
                            for pattern in bot_patterns:
                                if pattern.lower() in cmdline_str:
                                    # Extra validatie - check of het onze bot is
                                    if any(keyword in cmdline_str for keyword in ['telegram', 'trading', 'bot']):
                                        self.main_process = proc
                                        logger.info(f"✅ Found trading bot process: {pattern} PID {proc.info['pid']}")
                                        return True

                        # Check ook voor directe script uitvoering
                        for pattern in bot_patterns:
                            if pattern.lower() in cmdline_str:
                                self.main_process = proc
                                logger.info(f"✅ Found trading bot process: {pattern} PID {proc.info['pid']}")
                                return True

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            logger.warning("⚠️ Trading bot process not found")
            return False

        except Exception as e:
            logger.error(f"❌ Error finding main process: {e}")
            return False

    async def _check_main_process_status(self) -> str:
        """Check of main.py nog draait"""
        try:
            if not self.main_process:
                await self._find_main_process()
                if not self.main_process:
                    return "🔴 Niet gevonden"

            # Check if process is still running
            if self.main_process.is_running():
                # Check CPU and memory usage
                cpu_percent = self.main_process.cpu_percent()
                memory_info = self.main_process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024

                # Update system metrics
                self.system_metrics = {
                    'cpu_usage': cpu_percent,
                    'memory_usage_mb': memory_mb,
                    'memory_percent': self.main_process.memory_percent(),
                    'pid': self.main_process.pid,
                    'create_time': datetime.fromtimestamp(self.main_process.create_time())
                }

                if cpu_percent > 80:
                    return "🟡 Hoog CPU gebruik"
                elif memory_mb > 500:  # 500MB threshold
                    return "🟡 Hoog geheugengebruik"
                else:
                    return "🟢 Actief"
            else:
                self.main_process = None
                return "🔴 Gestopt"

        except psutil.NoSuchProcess:
            self.main_process = None
            return "🔴 Proces beëindigd"
        except Exception as e:
            logger.error(f"❌ Error checking process: {e}")
            return "🔴 Fout bij controle"

    async def _get_strategy_status(self) -> List[str]:
        """Haal actieve strategieën op"""
        try:
            # In een echte implementatie zou dit uit de strategy manager komen
            # Voor nu simuleren we enkele strategieën
            strategies = []

            # Check if we can import strategy manager
            try:
                from strategies.manager import StrategyManager
                # Als er een global strategy manager is, gebruik die
                # Anders return default strategies
                strategies = [
                    "Day Trading",
                    "Scalping",
                    "Momentum",
                    "Mean Reversion"
                ]
            except ImportError:
                strategies = ["Demo Strategy"]

            return strategies

        except Exception as e:
            logger.error(f"❌ Error getting strategies: {e}")
            return ["Fout bij ophalen strategieën"]

    async def _get_trade_info(self) -> Dict[str, Any]:
        """Haal laatste trade informatie op"""
        try:
            trade_manager = get_trade_manager()

            # Get last trade info
            last_trade = trade_manager.get_last_trade_info()

            # Get daily trade count
            daily_count = trade_manager.get_daily_trade_count()

            # Get trade statistics
            stats = trade_manager.get_trade_statistics()

            return {
                "last_trade": last_trade,
                "daily_count": daily_count,
                "statistics": stats
            }

        except Exception as e:
            logger.error(f"❌ Error getting trade info: {e}")
            return {
                "last_trade": None,
                "daily_count": 0,
                "statistics": {"total_trades": 0, "win_rate": 0}
            }

    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """Verzamel systeem metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()

            # Disk usage
            disk = psutil.disk_usage('/')

            # Network stats (optional)
            network = psutil.net_io_counters()

            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'disk_usage': disk.percent,
                'disk_free_gb': disk.free / (1024**3),
                'network_sent_mb': network.bytes_sent / (1024**2),
                'network_recv_mb': network.bytes_recv / (1024**2)
            }

        except Exception as e:
            logger.error(f"❌ Error collecting system metrics: {e}")
            return {}

    async def _send_heartbeat(self):
        """Stuur heartbeat naar Telegram admins"""
        try:
            # Collect all status information
            process_status = await self._check_main_process_status()
            strategies = await self._get_strategy_status()
            trade_info = await self._get_trade_info()
            system_metrics = await self._collect_system_metrics()

            # Calculate uptime
            uptime = datetime.now() - self.start_time
            uptime_str = str(uptime).split('.')[0]  # Remove microseconds

            # Prepare heartbeat data
            heartbeat_data = {
                'uptime': uptime_str,
                'status': process_status,
                'strategies': strategies,
                'last_trade': trade_info.get('last_trade'),
                'trade_count': trade_info.get('daily_count', 0),
                'system_metrics': system_metrics
            }

            # Send heartbeat
            success = await self.notifier.send_heartbeat(heartbeat_data)

            if success:
                self.last_heartbeat = datetime.now()
                logger.info("💓 Heartbeat sent successfully")
            else:
                logger.warning("⚠️ Failed to send heartbeat")

            # Send system status if there are issues
            if process_status.startswith("🔴") or process_status.startswith("🟡"):
                await self.notifier.send_alert(
                    alert_type="system",
                    message=f"Bot status: {process_status}",
                    priority="high" if "🔴" in process_status else "medium"
                )

        except Exception as e:
            logger.error(f"❌ Error sending heartbeat: {e}")

    async def run(self):
        """Start de heartbeat monitor"""
        if not await self.initialize():
            logger.error("❌ Failed to initialize monitor")
            return

        self.running = True
        logger.info("💓 Heartbeat Monitor started")

        # Send initial heartbeat
        await self.notifier.send_alert(
            alert_type="system",
            message="Heartbeat Monitor gestart en monitoring bot status",
            priority="normal"
        )

        try:
            while self.running:
                # Send heartbeat
                await self._send_heartbeat()

                # Wait for next interval
                await asyncio.sleep(self.heartbeat_interval)

        except KeyboardInterrupt:
            logger.info("💓 Heartbeat Monitor stopped by user")
        except Exception as e:
            logger.error(f"❌ Error in monitor loop: {e}")
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources"""
        self.running = False

        # Send shutdown notification
        try:
            await self.notifier.send_alert(
                alert_type="system",
                message="Heartbeat Monitor gestopt",
                priority="normal"
            )
        except:
            pass

        # Cleanup notifier
        await self.notifier.cleanup()
        logger.info("🧹 Heartbeat Monitor cleaned up")

async def main():
    """Main function"""
    logger.info("🚀 Starting Heartbeat Monitor...")

    monitor = HeartbeatMonitor()
    await monitor.run()

if __name__ == "__main__":
    # Setup basic logging
    logger.add(
        "logs/heartbeat.log",
        rotation="10 MB",
        retention="7 days",
        level="INFO"
    )

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("💓 Heartbeat Monitor shutdown complete")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)
