#!/usr/bin/env python3
"""
Futures Trading Manager - Safe leverage trading with comprehensive risk management
"""

import asyncio
import time
from typing import Dict, List, Optional, Union, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
from loguru import logger

class FuturesPosition:
    """Represents a futures position with leverage"""
    
    def __init__(self, symbol: str, side: str, size: float, entry_price: float, 
                 leverage: int, margin: float, liquidation_price: float):
        self.symbol = symbol
        self.side = side  # 'long' or 'short'
        self.size = size
        self.entry_price = entry_price
        self.leverage = leverage
        self.margin = margin
        self.liquidation_price = liquidation_price
        self.current_price = entry_price
        self.unrealized_pnl = 0.0
        self.unrealized_pnl_percentage = 0.0
        self.timestamp = datetime.now()
        self.stop_loss = None
        self.take_profit = None
        
    def update_price(self, current_price: float):
        """Update current price and calculate PnL"""
        self.current_price = current_price
        
        if self.side == 'long':
            price_diff = current_price - self.entry_price
        else:  # short
            price_diff = self.entry_price - current_price
            
        self.unrealized_pnl = (price_diff / self.entry_price) * self.size * self.leverage
        self.unrealized_pnl_percentage = (price_diff / self.entry_price) * 100 * self.leverage
        
    def is_liquidated(self) -> bool:
        """Check if position should be liquidated"""
        if self.side == 'long':
            return self.current_price <= self.liquidation_price
        else:  # short
            return self.current_price >= self.liquidation_price

class FuturesRiskManager:
    """Advanced risk management for futures trading"""
    
    def __init__(self, max_leverage: int = 5, max_position_size: float = 100.0):
        self.max_leverage = max_leverage
        self.max_position_size = max_position_size
        self.max_daily_loss = 0.05  # 5% max daily loss
        self.max_positions = 3  # Max 3 open positions
        self.daily_loss = 0.0
        self.last_reset = datetime.now().date()
        
    def check_leverage_risk(self, leverage: int, amount: float) -> Dict:
        """Check if leverage is within safe limits"""
        if leverage > self.max_leverage:
            return {
                "allowed": False,
                "reason": f"Leverage {leverage}x exceeds maximum {self.max_leverage}x"
            }
            
        if amount > self.max_position_size:
            return {
                "allowed": False,
                "reason": f"Position size €{amount} exceeds maximum €{self.max_position_size}"
            }
            
        return {"allowed": True, "reason": "Leverage within safe limits"}
    
    def check_daily_loss_limit(self, potential_loss: float) -> Dict:
        """Check if potential loss exceeds daily limit"""
        # Reset daily loss if new day
        if datetime.now().date() > self.last_reset:
            self.daily_loss = 0.0
            self.last_reset = datetime.now().date()
            
        total_potential_loss = self.daily_loss + potential_loss
        
        if total_potential_loss > self.max_daily_loss:
            return {
                "allowed": False,
                "reason": f"Daily loss limit exceeded: {total_potential_loss*100:.1f}% > {self.max_daily_loss*100:.1f}%"
            }
            
        return {"allowed": True, "reason": "Within daily loss limits"}

class FuturesManager:
    """Safe futures trading manager with comprehensive risk controls"""
    
    def __init__(self, exchange_manager=None):
        self.exchange_manager = exchange_manager
        self.risk_manager = FuturesRiskManager()
        self.positions: Dict[str, FuturesPosition] = {}
        self.monitoring_active = False
        
    async def create_futures_position(self, symbol: str, side: str, amount_eur: float, 
                                    leverage: int, exchange: str = "kucoin") -> Dict:
        """Create a new futures position with comprehensive safety checks"""
        try:
            logger.info(f"🎯 Creating futures position: {side} {symbol} €{amount_eur} {leverage}x on {exchange}")
            
            # SAFETY CHECK 1: Leverage limits
            leverage_check = self.risk_manager.check_leverage_risk(leverage, amount_eur)
            if not leverage_check["allowed"]:
                return {"success": False, "error": leverage_check["reason"]}
            
            # SAFETY CHECK 2: Daily loss limits
            max_loss = amount_eur  # Maximum possible loss is the margin
            loss_check = self.risk_manager.check_daily_loss_limit(max_loss / 1000)  # Convert to percentage
            if not loss_check["allowed"]:
                return {"success": False, "error": loss_check["reason"]}
            
            # SAFETY CHECK 3: Maximum positions
            if len(self.positions) >= self.risk_manager.max_positions:
                return {"success": False, "error": f"Maximum {self.risk_manager.max_positions} positions allowed"}
            
            # Get current price
            if not self.exchange_manager:
                return {"success": False, "error": "Exchange manager not available"}
                
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                return {"success": False, "error": f"Could not get price for {symbol}"}
                
            current_price = float(list(tickers.values())[0].last)
            
            # Calculate position parameters
            margin = amount_eur  # Margin is the amount we're risking
            position_size = amount_eur * leverage  # Total position value
            
            # Calculate liquidation price (simplified)
            if side == 'long':
                liquidation_price = current_price * (1 - 0.8 / leverage)  # 80% of margin
            else:  # short
                liquidation_price = current_price * (1 + 0.8 / leverage)
            
            # Create position object
            position = FuturesPosition(
                symbol=symbol,
                side=side,
                size=position_size,
                entry_price=current_price,
                leverage=leverage,
                margin=margin,
                liquidation_price=liquidation_price
            )
            
            # Set safety stops
            if side == 'long':
                position.stop_loss = current_price * 0.95  # 5% stop loss
                position.take_profit = current_price * 1.10  # 10% take profit
            else:  # short
                position.stop_loss = current_price * 1.05  # 5% stop loss
                position.take_profit = current_price * 0.90  # 10% take profit
            
            # Store position
            position_id = f"{symbol}_{side}_{int(time.time())}"
            self.positions[position_id] = position
            
            # Start monitoring if not already active
            if not self.monitoring_active:
                asyncio.create_task(self._monitor_positions())
                self.monitoring_active = True
            
            logger.info(f"✅ Futures position created: {position_id}")
            logger.info(f"   Entry: ${current_price:.2f}, Liquidation: ${liquidation_price:.2f}")
            logger.info(f"   Margin: €{margin}, Position Size: €{position_size}")
            
            return {
                "success": True,
                "position_id": position_id,
                "entry_price": current_price,
                "liquidation_price": liquidation_price,
                "margin": margin,
                "position_size": position_size,
                "leverage": leverage
            }
            
        except Exception as e:
            logger.error(f"❌ Error creating futures position: {e}")
            return {"success": False, "error": str(e)}
    
    async def close_position(self, position_id: str) -> Dict:
        """Close a futures position"""
        try:
            if position_id not in self.positions:
                return {"success": False, "error": "Position not found"}
            
            position = self.positions[position_id]
            
            # Get current price for final PnL calculation
            tickers = await self.exchange_manager.get_ticker_from_all(position.symbol)
            if tickers:
                current_price = float(list(tickers.values())[0].last)
                position.update_price(current_price)
            
            # Calculate final PnL
            final_pnl = position.unrealized_pnl
            final_pnl_percentage = position.unrealized_pnl_percentage
            
            # Remove position
            del self.positions[position_id]
            
            logger.info(f"✅ Closed futures position: {position_id}")
            logger.info(f"   Final PnL: €{final_pnl:.2f} ({final_pnl_percentage:+.2f}%)")
            
            return {
                "success": True,
                "final_pnl": final_pnl,
                "final_pnl_percentage": final_pnl_percentage,
                "exit_price": position.current_price
            }
            
        except Exception as e:
            logger.error(f"❌ Error closing position: {e}")
            return {"success": False, "error": str(e)}
    
    async def _monitor_positions(self):
        """Monitor all positions for liquidation and stop losses"""
        while self.positions:
            try:
                positions_to_close = []
                
                for position_id, position in self.positions.items():
                    # Get current price
                    tickers = await self.exchange_manager.get_ticker_from_all(position.symbol)
                    if not tickers:
                        continue
                        
                    current_price = float(list(tickers.values())[0].last)
                    position.update_price(current_price)
                    
                    # Check for liquidation
                    if position.is_liquidated():
                        logger.warning(f"🚨 LIQUIDATION: {position_id} at ${current_price:.2f}")
                        positions_to_close.append(position_id)
                        continue
                    
                    # Check stop loss
                    if position.side == 'long' and current_price <= position.stop_loss:
                        logger.warning(f"🛑 STOP LOSS: {position_id} at ${current_price:.2f}")
                        positions_to_close.append(position_id)
                        continue
                    elif position.side == 'short' and current_price >= position.stop_loss:
                        logger.warning(f"🛑 STOP LOSS: {position_id} at ${current_price:.2f}")
                        positions_to_close.append(position_id)
                        continue
                    
                    # Check take profit
                    if position.side == 'long' and current_price >= position.take_profit:
                        logger.info(f"🎯 TAKE PROFIT: {position_id} at ${current_price:.2f}")
                        positions_to_close.append(position_id)
                        continue
                    elif position.side == 'short' and current_price <= position.take_profit:
                        logger.info(f"🎯 TAKE PROFIT: {position_id} at ${current_price:.2f}")
                        positions_to_close.append(position_id)
                        continue
                
                # Close positions that need closing
                for position_id in positions_to_close:
                    await self.close_position(position_id)
                
                # Wait before next check
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Error in position monitoring: {e}")
                await asyncio.sleep(30)  # Wait longer on error
        
        self.monitoring_active = False
        logger.info("📊 Position monitoring stopped - no active positions")
    
    def get_positions_summary(self) -> Dict:
        """Get summary of all active positions"""
        if not self.positions:
            return {"active_positions": 0, "total_margin": 0, "total_unrealized_pnl": 0}
        
        total_margin = sum(pos.margin for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        positions_data = []
        for pos_id, pos in self.positions.items():
            positions_data.append({
                "id": pos_id,
                "symbol": pos.symbol,
                "side": pos.side,
                "leverage": pos.leverage,
                "margin": pos.margin,
                "entry_price": pos.entry_price,
                "current_price": pos.current_price,
                "liquidation_price": pos.liquidation_price,
                "unrealized_pnl": pos.unrealized_pnl,
                "unrealized_pnl_percentage": pos.unrealized_pnl_percentage
            })
        
        return {
            "active_positions": len(self.positions),
            "total_margin": total_margin,
            "total_unrealized_pnl": total_unrealized_pnl,
            "positions": positions_data
        }

# Global futures manager instance
futures_manager = None

def get_futures_manager(exchange_manager=None):
    """Get global futures manager instance"""
    global futures_manager
    if futures_manager is None:
        futures_manager = FuturesManager(exchange_manager)
    return futures_manager
