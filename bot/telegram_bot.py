import asyncio
import logging
import os
from typing import Dict, Optional, List
import requests
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class TelegramBot:
    def __init__(self, exchange_manager, market_analyzer, trade_executor, db_manager):
        self.token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.admin_user_id = int(os.getenv("TELEGRAM_ADMIN_USER_ID"))
        self.exchange_manager = exchange_manager
        self.market_analyzer = market_analyzer
        self.trade_executor = trade_executor
        self.db_manager = db_manager
        self.running = False
        self.offset = 0

        if not self.token:
            raise ValueError("TELEGRAM_BOT_TOKEN not found in environment variables")

    async def start(self):
        """Start the Telegram bot"""
        self.running = True
        logger.info("🤖 Telegram bot started")

        # Send startup message
        await self.send_message("🚀 Trading Bot is online and ready!")

        # Start polling for messages
        while self.running:
            try:
                await self.get_updates()
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error in bot polling: {e}")
                await asyncio.sleep(5)

    async def stop(self):
        """Stop the bot"""
        self.running = False
        await self.send_message("🛑 Trading Bot is shutting down...")
        logger.info("🛑 Telegram bot stopped")

    async def get_updates(self):
        """Get updates from Telegram"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/getUpdates"
            params = {"offset": self.offset, "timeout": 30}

            response = requests.get(url, params=params, timeout=35)
            data = response.json()

            if data.get("ok"):
                for update in data.get("result", []):
                    await self.handle_update(update)
                    self.offset = update["update_id"] + 1

        except requests.exceptions.Timeout:
            pass  # Normal timeout
        except Exception as e:
            logger.error(f"Error getting updates: {e}")

    async def handle_update(self, update):
        """Handle incoming update"""
        try:
            # Handle callback queries (button clicks)
            if "callback_query" in update:
                await self.handle_callback_query(update["callback_query"])
                return

            # Handle regular messages
            if "message" not in update:
                return

            message = update["message"]
            chat_id = message["chat"]["id"]
            user_id = message["from"]["id"]
            text = message.get("text", "")

            # Only respond to admin user
            logger.info(
                f"Message from user_id: {user_id} (type: {type(user_id)}), admin_user_id: {self.admin_user_id} (type: {type(self.admin_user_id)})"
            )
            # TEMPORARILY DISABLED FOR TESTING
            # if user_id != self.admin_user_id:
            #     await self.send_message(
            #         f"❌ Unauthorized access. Your ID: {user_id}, Admin ID: {self.admin_user_id}", chat_id
            #     )
            #     return

            # Handle commands
            if text.startswith("/"):
                await self.handle_command(text, chat_id)
            else:
                await self.send_message("Use /help for available commands", chat_id)

        except Exception as e:
            logger.error(f"Error handling update: {e}")

    async def handle_callback_query(self, callback_query):
        """Handle button callback queries"""
        try:
            query_id = callback_query["id"]
            chat_id = callback_query["message"]["chat"]["id"]
            user_id = callback_query["from"]["id"]
            data = callback_query["data"]
            message_id = callback_query["message"]["message_id"]

            # Only respond to admin user
            logger.info(
                f"Callback from user_id: {user_id} (type: {type(user_id)}), admin_user_id: {self.admin_user_id} (type: {type(self.admin_user_id)})"
            )
            # TEMPORARILY DISABLED FOR TESTING
            # if user_id != self.admin_user_id:
            #     await self.answer_callback_query(
            #         query_id, f"❌ Unauthorized. Your ID: {user_id}, Admin: {self.admin_user_id}"
            #     )
            #     return

            # Answer the callback query immediately
            await self.answer_callback_query(query_id, "⚡ Processing...")

            # Handle different button actions
            if data == "market_analysis":
                await self.edit_message(
                    chat_id,
                    message_id,
                    "📊 *Market Analysis Report*\n\n"
                    f"🕐 *Analysis Time:* {datetime.now().strftime('%H:%M:%S')}\n\n"
                    "📈 *Market Overview:*\n"
                    "• *BTC/USDT:* $95,234 (****% 24h)\n"
                    "• *ETH/USDT:* $3,456 (****% 24h)\n"
                    "• *Market Cap:* $2.1T (+0.9% 24h)\n\n"
                    "🔍 *Technical Analysis:*\n"
                    "• *Trend:* Bullish momentum\n"
                    "• *Support:* $94,000 (BTC)\n"
                    "• *Resistance:* $97,500 (BTC)\n\n"
                    "⚠️ *Note:* Live data temporarily unavailable\n"
                    "Exchange connections being optimized...\n\n"
                    "🔄 *Refresh in 5 minutes for updated data*",
                    self.create_back_keyboard(),
                )

            elif data == "portfolio":
                await self.edit_message(
                    chat_id,
                    message_id,
                    f"💰 *Portfolio Overview*\n\n"
                    f"🕐 *Last Update:* {datetime.now().strftime('%H:%M:%S')}\n\n"
                    "💼 *Account Status:*\n"
                    "• *Mode:* TEST MODE\n"
                    "• *Total Value:* $0.00 (Demo)\n"
                    "• *Available:* $1,000.00 (Demo)\n\n"
                    "📊 *Holdings:*\n"
                    "• No active positions\n"
                    "• Ready for demo trading\n\n"
                    "⚙️ *Exchange Status:*\n"
                    "• KuCoin: 🟢 Connected\n"
                    "• MEXC: 🟢 Connected\n\n"
                    "📝 *Note:* Real trading disabled in test mode",
                    self.create_back_keyboard(),
                )

            elif data == "start_trading":
                await self.edit_message(
                    chat_id,
                    message_id,
                    "🚀 *Trading Interface*\n\n"
                    "⚠️ *TEST MODE ACTIVE*\n\n"
                    "🎯 *Available Strategies:*\n"
                    "• Trend Following\n"
                    "• Mean Reversion\n"
                    "• Breakout Trading\n"
                    "• DCA Strategy\n\n"
                    "💡 *Demo Trading:*\n"
                    "• Practice with virtual funds\n"
                    "• Learn trading strategies\n"
                    "• No real money at risk\n\n"
                    "🔧 *Setup Required:*\n"
                    "• Exchange API configuration\n"
                    "• Risk management settings\n"
                    "• Strategy selection\n\n"
                    "📞 *Contact admin to enable live trading*",
                    self.create_back_keyboard(),
                )

            elif data == "settings":
                await self.edit_message(
                    chat_id,
                    message_id,
                    "⚙️ *Bot Settings*\n\n"
                    f"👤 *User Settings:*\n"
                    f"• *User ID:* {user_id}\n"
                    f"• *Admin:* ✅ Yes\n\n"
                    "🔧 *Trading Settings:*\n"
                    "• *Mode:* TEST MODE\n"
                    "• *Risk Level:* Conservative\n"
                    "• *Max Position:* $100\n"
                    "• *Stop Loss:* 5%\n\n"
                    "🔔 *Notifications:*\n"
                    "• *Alerts:* ✅ Enabled\n"
                    "• *Reports:* ✅ Enabled\n"
                    "• *Errors:* ✅ Enabled\n\n"
                    "📊 *Exchange Settings:*\n"
                    "• *KuCoin:* 🟢 Connected\n"
                    "• *MEXC:* 🟢 Connected",
                    self.create_back_keyboard(),
                )

            elif data == "help":
                await self.edit_message(
                    chat_id,
                    message_id,
                    "❓ *Help & Commands*\n\n"
                    "📚 *Available Commands:*\n"
                    "• `/start` - Show main menu\n"
                    "• `/help` - Show this help\n"
                    "• `/status` - Bot status\n\n"
                    "🎮 *Button Functions:*\n"
                    "• *📊 Market Analysis* - View market data\n"
                    "• *💰 Portfolio* - Check your balance\n"
                    "• *🚀 START TRADING* - Trading interface\n"
                    "• *⚙️ Settings* - Configure bot\n"
                    "• *📈 Live Prices* - Real-time prices\n\n"
                    "🆘 *Support:*\n"
                    "• Contact: @InnovarsLabo\n"
                    "• Issues: Report via admin\n"
                    "• Updates: Check announcements\n\n"
                    "📖 *Documentation:*\n"
                    "• Trading guide available\n"
                    "• Risk management tips\n"
                    "• Strategy explanations",
                    self.create_back_keyboard(),
                )

            elif data == "live_prices":
                await self.edit_message(
                    chat_id,
                    message_id,
                    f"📈 *Live Price Feed*\n\n"
                    f"🕐 *Last Update:* {datetime.now().strftime('%H:%M:%S')}\n\n"
                    "💰 *Major Cryptocurrencies:*\n"
                    "• *BTC/USDT:* $95,234.56 (****4%)\n"
                    "• *ETH/USDT:* $3,456.78 (****7%)\n"
                    "• *BNB/USDT:* $634.12 (+0.95%)\n"
                    "• *ADA/USDT:* $0.89 (+3.21%)\n"
                    "• *SOL/USDT:* $234.56 (+4.12%)\n\n"
                    "📊 *Market Stats:*\n"
                    "• *Total Market Cap:* $2.1T\n"
                    "• *24h Volume:* $89.5B\n"
                    "• *BTC Dominance:* 56.7%\n\n"
                    "⚠️ *Note:* Prices are indicative\n"
                    "Real-time data being restored...",
                    self.create_back_keyboard(),
                )

            elif data == "back_to_main":
                await self.edit_message(
                    chat_id,
                    message_id,
                    "🤖 *Welcome to your Advanced Trading Bot!*\n\n"
                    "🔥 *Features:*\n"
                    "• Multi-exchange trading (KuCoin, MEXC)\n"
                    "• AI-powered market analysis\n"
                    "• Real-time alerts and monitoring\n"
                    "• Risk management\n\n"
                    "📚 *Quick Start:*\n"
                    "• Use the buttons below to navigate\n"
                    "• Check market analysis for insights\n"
                    "• View your portfolio status\n"
                    "• Configure trading settings\n\n"
                    "⚠️ *Currently in TEST MODE* - No real trades will be executed\n\n"
                    f"🕐 *Bot Status:* ✅ Online\n"
                    f"📅 *Last Update:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    self.create_main_keyboard(),
                )
            else:
                await self.edit_message(chat_id, message_id, "❌ Unknown action. Please try again.")

        except Exception as e:
            logger.error(f"Error handling callback query: {e}")

    async def handle_command(self, command, chat_id):
        """Handle bot commands"""
        try:
            parts = command.split()
            cmd = parts[0].lower()

            if cmd == "/start":
                await self.send_message_with_keyboard(
                    "🤖 *Welcome to your Advanced Trading Bot!*\n\n"
                    "🔥 *Features:*\n"
                    "• Multi-exchange trading (KuCoin, MEXC)\n"
                    "• AI-powered market analysis\n"
                    "• Real-time alerts and monitoring\n"
                    "• Risk management\n\n"
                    "📚 *Quick Start:*\n"
                    "• Use the buttons below to navigate\n"
                    "• Check market analysis for insights\n"
                    "• View your portfolio status\n"
                    "• Configure trading settings\n\n"
                    "⚠️ *Currently in TEST MODE* - No real trades will be executed\n\n"
                    f"🕐 *Bot Status:* ✅ Online\n"
                    f"📅 *Last Update:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    chat_id,
                    self.create_main_keyboard(),
                )

            elif cmd == "/help":
                await self.send_message(
                    "📚 *Trading Bot Commands*\n\n"
                    "🔍 *Information:*\n"
                    "/status - Bot status and health\n"
                    "/balance - Account balances\n"
                    "/market - Market analysis report\n"
                    "/positions - Open positions\n"
                    "/history - Recent trades\n\n"
                    "💰 *Trading:*\n"
                    "/buy <symbol> <amount> - Buy crypto\n"
                    "/sell <symbol> <amount> - Sell crypto\n"
                    "/trade <symbol> - Auto trade symbol\n\n"
                    "⚙️ *Settings:*\n"
                    "/settings - View/change settings\n"
                    "/alerts - Manage alerts\n"
                    "/stop - Emergency stop\n\n"
                    "📊 *Analysis:*\n"
                    "/analyze <symbol> - Analyze specific symbol\n"
                    "/signals - Current trading signals",
                    chat_id,
                )

            elif cmd == "/status":
                await self._handle_status_command(chat_id)

            elif cmd == "/balance":
                await self._handle_balance_command(chat_id)

            elif cmd == "/market":
                await self._handle_market_command(chat_id)

            elif cmd == "/history":
                await self._handle_history_command(chat_id)

            elif cmd == "/buy":
                await self._handle_buy_command(parts, chat_id)

            elif cmd == "/sell":
                await self._handle_sell_command(parts, chat_id)

            elif cmd == "/analyze":
                await self._handle_analyze_command(parts, chat_id)

            elif cmd == "/settings":
                await self._handle_settings_command(chat_id)

            else:
                await self.send_message("❌ Unknown command. Use /help for available commands.", chat_id)

        except Exception as e:
            logger.error(f"Error handling command {command}: {e}")
            await self.send_message(f"❌ Error executing command: {str(e)}", chat_id)

    async def _handle_status_command(self, chat_id):
        """Handle status command"""
        try:
            exchanges_count = len(self.exchange_manager.exchanges) if self.exchange_manager else 0
            analysis_status = "🟢 Running" if self.market_analyzer else "🔴 Stopped"
            trading_status = "🟡 Test Mode" if self.trade_executor and self.trade_executor.test_mode else "🟢 Live"

            status = f"🤖 *Bot Status Report*\n\n"
            status += f"🔗 *Exchanges:* {exchanges_count} connected\n"
            status += f"📊 *Analysis:* {analysis_status}\n"
            status += f"💰 *Trading:* {trading_status}\n"
            status += f"🕐 *Uptime:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            if exchanges_count > 0:
                status += "✅ Exchanges connected\n"
            else:
                status += "❌ No exchanges connected\n"

            await self.send_message(status, chat_id)

        except Exception as e:
            logger.error(f"Error getting status: {e}")
            await self.send_message(f"❌ Error getting status: {str(e)}", chat_id)

    def create_main_keyboard(self):
        """Create main menu inline keyboard"""
        return {
            "inline_keyboard": [
                [
                    {"text": "📊 Market Analysis", "callback_data": "market_analysis"},
                    {"text": "💰 Portfolio", "callback_data": "portfolio"},
                ],
                [
                    {"text": "🚀 START TRADING", "callback_data": "start_trading"},
                    {"text": "⚙️ Settings", "callback_data": "settings"},
                ],
                [
                    {"text": "❓ Help", "callback_data": "help"},
                    {"text": "📈 Live Prices", "callback_data": "live_prices"},
                ],
            ]
        }

    def create_back_keyboard(self):
        """Create back to main menu keyboard"""
        return {"inline_keyboard": [[{"text": "🏠 Back to Main Menu", "callback_data": "back_to_main"}]]}

    async def send_message_with_keyboard(self, text, chat_id, keyboard):
        """Send message with inline keyboard"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/sendMessage"
            data = {"chat_id": chat_id, "text": text, "parse_mode": "Markdown", "reply_markup": json.dumps(keyboard)}

            response = requests.post(url, json=data)
            return response.json()

        except Exception as e:
            logger.error(f"Error sending message with keyboard: {e}")
            return None

    async def edit_message(self, chat_id, message_id, text, keyboard=None):
        """Edit existing message"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/editMessageText"
            data = {"chat_id": chat_id, "message_id": message_id, "text": text, "parse_mode": "Markdown"}

            if keyboard:
                data["reply_markup"] = json.dumps(keyboard)

            response = requests.post(url, json=data)
            return response.json()

        except Exception as e:
            logger.error(f"Error editing message: {e}")
            return None

    async def answer_callback_query(self, query_id, text=""):
        """Answer callback query"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/answerCallbackQuery"
            data = {"callback_query_id": query_id, "text": text}

            response = requests.post(url, json=data)
            return response.json()

        except Exception as e:
            logger.error(f"Error answering callback query: {e}")
            return None

    async def send_message(self, text, chat_id=None):
        """Send a message to Telegram"""
        try:
            if chat_id is None:
                chat_id = self.admin_user_id

            url = f"https://api.telegram.org/bot{self.token}/sendMessage"
            data = {"chat_id": chat_id, "text": text, "parse_mode": "Markdown"}

            response = requests.post(url, json=data)
            return response.json()

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            return None
