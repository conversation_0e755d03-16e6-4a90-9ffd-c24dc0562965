import asyncio
import logging
import os
from typing import Dict, Optional, List
import requests
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class TelegramBot:
    def __init__(self, exchange_manager, market_analyzer, trade_executor, db_manager):
        self.token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.admin_user_id = int(os.getenv('TELEGRAM_ADMIN_USER_ID'))
        self.exchange_manager = exchange_manager
        self.market_analyzer = market_analyzer
        self.trade_executor = trade_executor
        self.db_manager = db_manager
        self.running = False
        self.offset = 0

        if not self.token:
            raise ValueError("TELEGRAM_BOT_TOKEN not found in environment variables")

    async def start(self):
        """Start the Telegram bot"""
        self.running = True
        logger.info("🤖 Telegram bot started")

        # Send startup message
        await self.send_message("🚀 Trading Bot is online and ready!")

        # Start polling for messages
        while self.running:
            try:
                await self.get_updates()
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error in bot polling: {e}")
                await asyncio.sleep(5)

    async def stop(self):
        """Stop the bot"""
        self.running = False
        await self.send_message("🛑 Trading Bot is shutting down...")
        logger.info("🛑 Telegram bot stopped")

    async def get_updates(self):
        """Get updates from Telegram"""
        try:
            url = f"https://api.telegram.org/bot{self.token}/getUpdates"
            params = {
                'offset': self.offset,
                'timeout': 30
            }

            response = requests.get(url, params=params, timeout=35)
            data = response.json()

            if data.get('ok'):
                for update in data.get('result', []):
                    await self.handle_update(update)
                    self.offset = update['update_id'] + 1

        except requests.exceptions.Timeout:
            pass  # Normal timeout
        except Exception as e:
            logger.error(f"Error getting updates: {e}")

    async def handle_update(self, update):
        """Handle incoming update"""
        try:
            if 'message' not in update:
                return

            message = update['message']
            chat_id = message['chat']['id']
            user_id = message['from']['id']
            text = message.get('text', '')

            # Only respond to admin user
            if user_id != self.admin_user_id:
                await self.send_message("❌ Unauthorized access", chat_id)
                return

            # Handle commands
            if text.startswith('/'):
                await self.handle_command(text, chat_id)
            else:
                await self.send_message("Use /help for available commands", chat_id)

        except Exception as e:
            logger.error(f"Error handling update: {e}")

    async def handle_command(self, command, chat_id):
        """Handle bot commands"""
        try:
            parts = command.split()
            cmd = parts[0].lower()

            if cmd == '/start':
                await self.send_message(
                    "🤖 *Welcome to your Advanced Trading Bot!*\n\n"
                    "🔥 *Features:*\n"
                    "• Multi-exchange trading (KuCoin, MEXC)\n"
                    "• AI-powered market analysis\n"
                    "• Real-time alerts and monitoring\n"
                    "• Risk management\n\n"
                    "📚 *Quick Start:*\n"
                    "/help - Show all commands\n"
                    "/status - Check bot status\n"
                    "/balance - View balances\n"
                    "/market - Market analysis\n\n"
                    "⚠️ *Currently in TEST MODE* - No real trades will be executed",
                    chat_id
                )

            elif cmd == '/help':
                await self.send_message(
                    "📚 *Trading Bot Commands*\n\n"
                    "🔍 *Information:*\n"
                    "/status - Bot status and health\n"
                    "/balance - Account balances\n"
                    "/market - Market analysis report\n"
                    "/positions - Open positions\n"
                    "/history - Recent trades\n\n"
                    "💰 *Trading:*\n"
                    "/buy <symbol> <amount> - Buy crypto\n"
                    "/sell <symbol> <amount> - Sell crypto\n"
                    "/trade <symbol> - Auto trade symbol\n\n"
                    "⚙️ *Settings:*\n"
                    "/settings - View/change settings\n"
                    "/alerts - Manage alerts\n"
                    "/stop - Emergency stop\n\n"
                    "📊 *Analysis:*\n"
                    "/analyze <symbol> - Analyze specific symbol\n"
                    "/signals - Current trading signals",
                    chat_id
                )

            elif cmd == '/status':
                await self._handle_status_command(chat_id)

            elif cmd == '/balance':
                await self._handle_balance_command(chat_id)

            elif cmd == '/market':
                await self._handle_market_command(chat_id)

            elif cmd == '/history':
                await self._handle_history_command(chat_id)

            elif cmd == '/buy':
                await self._handle_buy_command(parts, chat_id)

            elif cmd == '/sell':
                await self._handle_sell_command(parts, chat_id)

            elif cmd == '/analyze':
                await self._handle_analyze_command(parts, chat_id)

            elif cmd == '/settings':
                await self._handle_settings_command(chat_id)

            else:
                await self.send_message(
                    "❌ Unknown command. Use /help for available commands.",
                    chat_id
                )

        except Exception as e:
            logger.error(f"Error handling command {command}: {e}")
            await self.send_message(f"❌ Error executing command: {str(e)}", chat_id)

    async def _handle_status_command(self, chat_id):
        """Handle status command"""
        try:
            exchanges_count = len(self.exchange_manager.exchanges) if self.exchange_manager else 0
            analysis_status = "🟢 Running" if self.market_analyzer else "🔴 Stopped"
            trading_status = "🟡 Test Mode" if self.trade_executor and self.trade_executor.test_mode else "🟢 Live"

            status = f"🤖 *Bot Status Report*\n\n"
            status += f"🔗 *Exchanges:* {exchanges_count} connected\n"
            status += f"📊 *Analysis:* {analysis_status}\n"
            status += f"💰 *Trading:* {trading_status}\n"
            status += f"🕐 *Uptime:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            if exchanges_count > 0:
                status += "✅
