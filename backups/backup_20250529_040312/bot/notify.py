"""
Telegram notificatie systeem voor bot monitoring en alerts
"""
import asyncio
import aiohttp
import json
import ssl
import certifi
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger
from config.settings import Settings

class TelegramNotifier:
    """
    Telegram notificatie klasse voor het versturen van status updates,
    alerts en monitoring berichten naar admin gebruikers.
    """
    
    def __init__(self, settings: Settings = None):
        self.settings = settings or Settings()
        self.bot_token = self.settings.telegram_bot_token
        self.admin_user_ids = self.settings.telegram_admin_user_ids
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.session: Optional[aiohttp.ClientSession] = None
        self._initialized = False
        
    async def initialize(self) -> bool:
        """Initialize de notifier"""
        try:
            # Create SSL context
            ssl_context = ssl.create_default_context(cafile=certifi.where())
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(connector=connector)
            
            # Test bot token
            url = f"{self.base_url}/getMe"
            async with self.session.get(url) as response:
                result = await response.json()
                if result.get('ok'):
                    self._initialized = True
                    logger.info("✅ Telegram notifier initialized")
                    return True
                else:
                    logger.error("❌ Invalid bot token for notifier")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error initializing notifier: {e}")
            return False
    
    async def send_to_admins(self, message: str, parse_mode: str = "Markdown") -> bool:
        """Stuur bericht naar alle admin gebruikers"""
        if not self._initialized:
            if not await self.initialize():
                return False
        
        success_count = 0
        for admin_id in self.admin_user_ids:
            if await self._send_message(admin_id, message, parse_mode):
                success_count += 1
        
        return success_count > 0
    
    async def _send_message(self, chat_id: int, text: str, parse_mode: str = "Markdown") -> bool:
        """Stuur bericht naar specifieke chat"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": parse_mode,
                "disable_web_page_preview": True
            }
            
            async with self.session.post(url, json=data) as response:
                result = await response.json()
                if result.get('ok'):
                    return True
                else:
                    logger.warning(f"Failed to send message to {chat_id}: {result}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error sending message to {chat_id}: {e}")
            return False
    
    async def send_heartbeat(self, status_data: Dict[str, Any]) -> bool:
        """Stuur heartbeat status bericht"""
        try:
            uptime = status_data.get('uptime', 'Unknown')
            bot_status = status_data.get('status', 'Unknown')
            strategies = status_data.get('strategies', [])
            last_trade = status_data.get('last_trade', None)
            trade_count = status_data.get('trade_count', 0)
            
            # Format strategy names
            strategy_list = ", ".join(strategies) if strategies else "Geen actieve strategieën"
            
            # Format last trade info
            if last_trade:
                trade_info = f"🔄 **Laatste Trade:** {last_trade['symbol']} {last_trade['side']} @ {last_trade['timestamp']}"
            else:
                trade_info = "🔄 **Laatste Trade:** Geen trades vandaag"
            
            message = f"""
💓 **Bot Heartbeat**

⏱️ **Uptime:** {uptime}
🤖 **Status:** {bot_status}
📊 **Trades Vandaag:** {trade_count}

🎯 **Actieve Strategieën:**
{strategy_list}

{trade_info}

📅 **Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            return await self.send_to_admins(message)
            
        except Exception as e:
            logger.error(f"❌ Error sending heartbeat: {e}")
            return False
    
    async def send_alert(self, alert_type: str, message: str, priority: str = "normal") -> bool:
        """Stuur alert bericht"""
        try:
            # Emoji mapping voor alert types
            emoji_map = {
                "error": "🚨",
                "warning": "⚠️",
                "info": "ℹ️",
                "success": "✅",
                "trade": "💰",
                "system": "🔧"
            }
            
            emoji = emoji_map.get(alert_type, "📢")
            
            # Priority formatting
            if priority == "high":
                priority_text = "🔴 **HIGH PRIORITY**"
            elif priority == "medium":
                priority_text = "🟡 **MEDIUM PRIORITY**"
            else:
                priority_text = "🟢 **NORMAL**"
            
            alert_message = f"""
{emoji} **ALERT: {alert_type.upper()}**

{priority_text}

{message}

📅 **Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            return await self.send_to_admins(alert_message)
            
        except Exception as e:
            logger.error(f"❌ Error sending alert: {e}")
            return False
    
    async def send_trade_notification(self, trade_data: Dict[str, Any]) -> bool:
        """Stuur trade notificatie"""
        try:
            symbol = trade_data.get('symbol', 'Unknown')
            side = trade_data.get('side', 'Unknown')
            amount = trade_data.get('amount', 0)
            price = trade_data.get('price', 0)
            strategy = trade_data.get('strategy', 'Unknown')
            exchange = trade_data.get('exchange', 'Unknown')
            status = trade_data.get('status', 'Unknown')
            
            # Side emoji
            side_emoji = "🟢" if side.lower() == "buy" else "🔴"
            
            message = f"""
💰 **Trade Uitgevoerd**

{side_emoji} **{side.upper()}** {symbol}
💎 **Amount:** {amount}
💵 **Price:** ${price}
🏪 **Exchange:** {exchange.upper()}
🎯 **Strategy:** {strategy}
📊 **Status:** {status}

📅 **Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            return await self.send_to_admins(message)
            
        except Exception as e:
            logger.error(f"❌ Error sending trade notification: {e}")
            return False
    
    async def send_performance_report(self, performance_data: Dict[str, Any]) -> bool:
        """Stuur performance rapport"""
        try:
            total_pnl = performance_data.get('total_pnl', 0)
            win_rate = performance_data.get('win_rate', 0)
            total_trades = performance_data.get('total_trades', 0)
            best_trade = performance_data.get('best_trade', 0)
            worst_trade = performance_data.get('worst_trade', 0)
            
            # PnL emoji
            pnl_emoji = "📈" if total_pnl >= 0 else "📉"
            
            message = f"""
📊 **Performance Rapport**

{pnl_emoji} **Total P&L:** ${total_pnl:.2f}
🎯 **Win Rate:** {win_rate:.1f}%
📈 **Total Trades:** {total_trades}
🏆 **Best Trade:** ${best_trade:.2f}
📉 **Worst Trade:** ${worst_trade:.2f}

📅 **Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            return await self.send_to_admins(message)
            
        except Exception as e:
            logger.error(f"❌ Error sending performance report: {e}")
            return False
    
    async def send_system_status(self, system_data: Dict[str, Any]) -> bool:
        """Stuur systeem status"""
        try:
            cpu_usage = system_data.get('cpu_usage', 0)
            memory_usage = system_data.get('memory_usage', 0)
            disk_usage = system_data.get('disk_usage', 0)
            exchange_status = system_data.get('exchanges', {})
            
            # Format exchange status
            exchange_text = ""
            for exchange, status in exchange_status.items():
                status_emoji = "✅" if status else "❌"
                exchange_text += f"{status_emoji} {exchange.upper()}\n"
            
            message = f"""
🔧 **System Status**

💻 **Resources:**
• CPU: {cpu_usage:.1f}%
• Memory: {memory_usage:.1f}%
• Disk: {disk_usage:.1f}%

🏪 **Exchanges:**
{exchange_text}

📅 **Checked:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            return await self.send_to_admins(message)
            
        except Exception as e:
            logger.error(f"❌ Error sending system status: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
        logger.info("🧹 Telegram notifier cleaned up")

# Global notifier instance
_notifier = None

def get_notifier() -> TelegramNotifier:
    """Get global notifier instance"""
    global _notifier
    if _notifier is None:
        _notifier = TelegramNotifier()
    return _notifier
