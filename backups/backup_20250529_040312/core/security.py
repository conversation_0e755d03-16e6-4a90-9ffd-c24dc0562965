"""
Beveiligingsmodule voor de trading bot
"""
import os
import hashlib
import hmac
import base64
import secrets
from typing import Dict, Optional, Tuple
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from loguru import logger
import time
from functools import wraps

class SecurityManager:
    """
    Centralized security management voor API keys en gevoelige data
    
    Deze klasse beheert encryptie, API key beveiliging, en access control
    voor de trading bot.
    """
    
    def __init__(self, encryption_key: Optional[str] = None):
        self.encryption_key = encryption_key
        self._cipher_suite = None
        self._failed_attempts = {}
        self._session_tokens = {}
        self._setup_encryption()
        
    def _setup_encryption(self):
        """Setup encryption voor gevoelige data"""
        if self.encryption_key:
            # Derive key from password
            password = self.encryption_key.encode()
            salt = b'trading_bot_salt_2024'  # In productie: gebruik random salt
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self._cipher_suite = Fernet(key)
            logger.info("✅ Encryption initialized")
        else:
            logger.warning("⚠️ No encryption key provided - sensitive data not encrypted")
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt gevoelige data"""
        if not self._cipher_suite:
            logger.warning("⚠️ No encryption available - returning plain text")
            return data
            
        try:
            encrypted_data = self._cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"❌ Encryption failed: {e}")
            return data
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt gevoelige data"""
        if not self._cipher_suite:
            logger.warning("⚠️ No encryption available - returning as-is")
            return encrypted_data
            
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"❌ Decryption failed: {e}")
            return encrypted_data
    
    def secure_api_credentials(self, credentials: Dict[str, str]) -> Dict[str, str]:
        """Beveilig API credentials"""
        secured_creds = {}
        for key, value in credentials.items():
            if value and any(sensitive in key.lower() for sensitive in ['key', 'secret', 'token', 'passphrase']):
                secured_creds[key] = self.encrypt_data(value)
            else:
                secured_creds[key] = value
        return secured_creds
    
    def get_api_credentials(self, credentials: Dict[str, str]) -> Dict[str, str]:
        """Haal beveiligde API credentials op"""
        decrypted_creds = {}
        for key, value in credentials.items():
            if value and any(sensitive in key.lower() for sensitive in ['key', 'secret', 'token', 'passphrase']):
                decrypted_creds[key] = self.decrypt_data(value)
            else:
                decrypted_creds[key] = value
        return decrypted_creds
    
    def validate_api_signature(self, api_key: str, secret: str, timestamp: str, 
                              method: str, path: str, body: str = "") -> str:
        """Genereer API signature voor exchange requests"""
        try:
            # KuCoin style signature
            str_to_sign = timestamp + method.upper() + path + body
            signature = base64.b64encode(
                hmac.new(secret.encode(), str_to_sign.encode(), hashlib.sha256).digest()
            ).decode()
            return signature
        except Exception as e:
            logger.error(f"❌ Signature generation failed: {e}")
            return ""
    
    def check_rate_limit(self, identifier: str, max_requests: int = 10, 
                        window_seconds: int = 60) -> bool:
        """Check rate limiting voor API calls"""
        current_time = time.time()
        
        if identifier not in self._failed_attempts:
            self._failed_attempts[identifier] = []
        
        # Clean old attempts
        self._failed_attempts[identifier] = [
            attempt_time for attempt_time in self._failed_attempts[identifier]
            if current_time - attempt_time < window_seconds
        ]
        
        # Check if under limit
        if len(self._failed_attempts[identifier]) >= max_requests:
            logger.warning(f"⚠️ Rate limit exceeded for {identifier}")
            return False
        
        # Add current attempt
        self._failed_attempts[identifier].append(current_time)
        return True
    
    def generate_session_token(self, user_id: int) -> str:
        """Genereer session token voor gebruiker"""
        token = secrets.token_urlsafe(32)
        self._session_tokens[token] = {
            'user_id': user_id,
            'created_at': time.time(),
            'expires_at': time.time() + 3600  # 1 hour
        }
        return token
    
    def validate_session_token(self, token: str) -> Optional[int]:
        """Valideer session token"""
        if token not in self._session_tokens:
            return None
        
        session = self._session_tokens[token]
        if time.time() > session['expires_at']:
            del self._session_tokens[token]
            return None
        
        return session['user_id']
    
    def mask_sensitive_data(self, data: str, show_chars: int = 4) -> str:
        """Mask gevoelige data voor logging"""
        if not data or len(data) <= show_chars:
            return "[MASKED]"
        return f"{'*' * (len(data) - show_chars)}{data[-show_chars:]}"

def require_auth(authorized_users: list):
    """Decorator voor authenticatie vereiste"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Assuming first arg is self and has user_id
            if hasattr(args[0], 'user_id'):
                user_id = args[0].user_id
                if user_id not in authorized_users:
                    logger.warning(f"🚫 Unauthorized access attempt by user {user_id}")
                    return {"error": "Unauthorized access"}
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def rate_limit(max_calls: int = 10, window_seconds: int = 60):
    """Decorator voor rate limiting"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Use function name as identifier
            identifier = f"{func.__name__}_{id(args[0]) if args else 'global'}"
            
            security_manager = SecurityManager()
            if not security_manager.check_rate_limit(identifier, max_calls, window_seconds):
                logger.warning(f"⚠️ Rate limit exceeded for {func.__name__}")
                return {"error": "Rate limit exceeded"}
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Global security manager instance
_security_manager = None

def get_security_manager(encryption_key: Optional[str] = None) -> SecurityManager:
    """Get global security manager instance"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager(encryption_key)
    return _security_manager
