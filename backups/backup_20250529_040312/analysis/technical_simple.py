"""
Simplified Technical Analysis Module (Python 3.13 compatible)
"""
import asyncio
from typing import Dict, List, Optional
from decimal import Decimal
from loguru import logger
import math
import random

class TechnicalAnalyzer:
    """Simplified technical analysis for trading strategies"""
    
    def __init__(self):
        self.indicators = {}
    
    async def analyze(self, symbol: str, market_data: Dict, timeframe: str = "1h") -> Dict:
        """Perform simplified technical analysis"""
        try:
            # Get basic price data
            current_price = market_data['price']
            high_24h = market_data.get('high_24h', current_price)
            low_24h = market_data.get('low_24h', current_price)
            volume = market_data.get('volume', 0)
            
            # Generate simulated historical data for analysis
            price_data = self._generate_price_data(current_price, 100)
            
            indicators = {}
            
            # Basic trend indicators
            indicators.update(self._calculate_moving_averages(price_data))
            
            # Momentum indicators
            indicators.update(self._calculate_momentum_indicators(price_data, current_price))
            
            # Volatility indicators
            indicators.update(self._calculate_volatility_indicators(price_data, current_price))
            
            # Support/Resistance levels
            indicators.update(self._calculate_support_resistance(high_24h, low_24h, current_price))
            
            # Overall score
            indicators['score'] = self._calculate_overall_score(indicators)
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error in technical analysis for {symbol}: {e}")
            return {"error": str(e), "score": 0}
    
    def _generate_price_data(self, current_price: float, periods: int) -> List[float]:
        """Generate simulated price data for analysis"""
        prices = []
        price = current_price
        
        # Use deterministic seed for consistent results
        random.seed(42)
        
        for i in range(periods):
            # Simulate price movement with mean reversion
            change = random.uniform(-0.03, 0.03)  # 3% max change
            price = price * (1 + change)
            prices.append(price)
        
        return prices
    
    def _calculate_moving_averages(self, prices: List[float]) -> Dict:
        """Calculate moving averages"""
        try:
            indicators = {}
            
            # Simple Moving Averages
            indicators['sma_20'] = self._sma(prices, 20)
            indicators['sma_50'] = self._sma(prices, 50)
            
            # Exponential Moving Averages
            indicators['ema_12'] = self._ema(prices, 12)
            indicators['ema_26'] = self._ema(prices, 26)
            
            # MACD
            macd = indicators['ema_12'] - indicators['ema_26']
            indicators['macd'] = macd
            indicators['macd_signal'] = macd * 0.9  # Simplified signal line
            
            return indicators
            
        except Exception:
            return {}
    
    def _calculate_momentum_indicators(self, prices: List[float], current_price: float) -> Dict:
        """Calculate momentum indicators"""
        try:
            indicators = {}
            
            # RSI (simplified)
            indicators['rsi'] = self._calculate_rsi(prices)
            
            # Rate of Change
            if len(prices) >= 14:
                roc = ((prices[-1] - prices[-14]) / prices[-14]) * 100
                indicators['roc_14'] = roc
            else:
                indicators['roc_14'] = 0
            
            # Price momentum
            if len(prices) >= 10:
                momentum = ((prices[-1] - prices[-10]) / prices[-10]) * 100
                indicators['momentum_10'] = momentum
            else:
                indicators['momentum_10'] = 0
            
            return indicators
            
        except Exception:
            return {}
    
    def _calculate_volatility_indicators(self, prices: List[float], current_price: float) -> Dict:
        """Calculate volatility indicators"""
        try:
            indicators = {}
            
            # Bollinger Bands (simplified)
            sma_20 = self._sma(prices, 20)
            std_dev = self._calculate_std_dev(prices[-20:] if len(prices) >= 20 else prices)
            
            indicators['bb_upper'] = sma_20 + (2 * std_dev)
            indicators['bb_middle'] = sma_20
            indicators['bb_lower'] = sma_20 - (2 * std_dev)
            
            # Average True Range (simplified)
            indicators['atr'] = std_dev * 1.5
            
            return indicators
            
        except Exception:
            return {}
    
    def _calculate_support_resistance(self, high_24h: float, low_24h: float, current_price: float) -> Dict:
        """Calculate support and resistance levels"""
        try:
            # Pivot points
            pivot = (high_24h + low_24h + current_price) / 3
            
            return {
                'pivot': pivot,
                'resistance_1': 2 * pivot - low_24h,
                'support_1': 2 * pivot - high_24h,
                'resistance_2': pivot + (high_24h - low_24h),
                'support_2': pivot - (high_24h - low_24h),
                'high_24h': high_24h,
                'low_24h': low_24h
            }
            
        except Exception:
            return {}
    
    def _sma(self, prices: List[float], period: int) -> float:
        """Simple Moving Average"""
        if len(prices) < period:
            return prices[-1] if prices else 0
        return sum(prices[-period:]) / period
    
    def _ema(self, prices: List[float], period: int) -> float:
        """Exponential Moving Average"""
        if len(prices) < period:
            return prices[-1] if prices else 0
        
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
        
        return ema
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Simplified RSI calculation"""
        if len(prices) < period + 1:
            return 50  # Neutral
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return 50
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_std_dev(self, prices: List[float]) -> float:
        """Calculate standard deviation"""
        if len(prices) < 2:
            return 0
        
        mean = sum(prices) / len(prices)
        variance = sum((price - mean) ** 2 for price in prices) / len(prices)
        return math.sqrt(variance)
    
    def _calculate_overall_score(self, indicators: Dict) -> float:
        """Calculate overall technical score (-100 to +100)"""
        try:
            score = 0.0
            signals = 0
            
            # Trend signals
            if 'sma_20' in indicators and 'sma_50' in indicators:
                if indicators['sma_20'] > indicators['sma_50']:
                    score += 20
                else:
                    score -= 20
                signals += 1
            
            # MACD signal
            if 'macd' in indicators and 'macd_signal' in indicators:
                if indicators['macd'] > indicators['macd_signal']:
                    score += 15
                else:
                    score -= 15
                signals += 1
            
            # RSI signal
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                if rsi < 30:
                    score += 25  # Oversold
                elif rsi > 70:
                    score -= 25  # Overbought
                elif 40 < rsi < 60:
                    score += 5   # Neutral is slightly positive
                signals += 1
            
            # Bollinger Bands
            if all(k in indicators for k in ['bb_upper', 'bb_lower', 'bb_middle']):
                # Assuming current price is close to bb_middle for this calculation
                current_price = indicators['bb_middle']
                if current_price < indicators['bb_lower']:
                    score += 20  # Oversold
                elif current_price > indicators['bb_upper']:
                    score -= 20  # Overbought
                signals += 1
            
            # Normalize score
            if signals > 0:
                score = score / signals * 2  # Scale to -100 to +100
                return max(-100, min(100, score))
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating overall score: {e}")
            return 0.0
