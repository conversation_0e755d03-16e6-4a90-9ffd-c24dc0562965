"""
Telegram bot command handlers
"""
from typing import List
from .telegram_handler import CommandHandler, TelegramMessage
from loguru import logger

class StartCommand(CommandHandler):
    """Start command handler"""

    def __init__(self):
        super().__init__(
            name="start",
            description="Start de bot en toon welkomstbericht",
            admin_only=False
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle start command"""
        welcome_message = """
🤖 **Welkom bij de AI Trading Bot!**

🚀 **Functies:**
• Automatische trading met AI-strategieën
• Real-time marktanalyse
• Portfolio tracking
• Risk management
• Live prijzen en alerts

📊 **Beschikbare Commando's:**
• `/ping` - Test bot verbinding
• `/balance` - Toon portfolio balances
• `/price <symbol>` - Toon prijs (bijv. /price BTC/USDT)
• `/analysis` - Marktanalyse
• `/trading` - Trading dashboard
• `/help` - Toon alle commando's

🎓 **Leer crypto trading** met real-time data en AI-analyse!
⚠️ **Let op:** Dit is een demo bot voor educatieve doeleinden.

Gebruik de knoppen hieronder voor snelle toegang tot functies.
        """

        # Send welcome message with permanent keyboard
        keyboard = {
            "keyboard": [
                [
                    {"text": "🚀 START TRADING"},
                    {"text": "💰 Portfolio"}
                ],
                [
                    {"text": "📊 Market Analysis"},
                    {"text": "📈 Live Prices"}
                ],
                [
                    {"text": "🤖 Trading Controls"},
                    {"text": "❓ Help"}
                ]
            ],
            "resize_keyboard": True,
            "one_time_keyboard": False,
            "persistent": True
        }

        await bot.send_message(message.chat_id, welcome_message, reply_markup=keyboard)
        return None  # Message already sent

class HelpCommand(CommandHandler):
    """Help command handler"""

    def __init__(self):
        super().__init__(
            name="help",
            description="Toon alle beschikbare commando's",
            admin_only=False
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle help command"""
        help_message = """
❓ **Help & Commando's**

**🤖 Bot Commando's:**
• `/start` - Start de bot
• `/ping` - Test verbinding
• `/help` - Toon deze help

**💰 Portfolio:**
• `/balance` - Toon balances
• `/portfolio` - Portfolio overzicht

**📊 Markt & Analyse:**
• `/price <symbol>` - Toon prijs
• `/analysis` - Marktanalyse
• `/signals` - Trading signalen

**🚀 Trading:**
• `/trading` - Trading dashboard
• `/positions` - Open posities
• `/history` - Trading geschiedenis

**⚙️ Instellingen:**
• `/settings` - Bot instellingen
• `/risk` - Risk management

**🆘 Support:**
• `/status` - Bot status
• `/logs` - Recente logs

**💡 Tips:**
• Gebruik knoppen voor snelle toegang
• Start altijd met kleine bedragen
• Monitor je risico regelmatig
        """
        return help_message

class PingCommand(CommandHandler):
    """Ping command handler"""

    def __init__(self):
        super().__init__(
            name="ping",
            description="Test bot verbinding",
            admin_only=False
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle ping command"""
        return "Bot is alive 🟢"

class BalanceCommand(CommandHandler):
    """Balance command handler"""

    def __init__(self):
        super().__init__(
            name="balance",
            description="Toon portfolio balances",
            admin_only=True
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle balance command"""
        try:
            # This would integrate with the exchange manager
            # For now, return a placeholder
            return """
💰 **Portfolio Balances**

**KuCoin (Sandbox):**
• USDT: 1,000.00
• BTC: 0.00000000
• ETH: 0.00000000

**MEXC (Sandbox):**
• USDT: 1,000.00
• BTC: 0.00000000
• ETH: 0.00000000

🔧 **Debug Info:**
• KuCoin Sandbox: Ja
• MEXC Sandbox: Ja
• Live Mode: Nee

💡 **Tip:** Dit zijn sandbox balances voor testing.
            """
        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            return "❌ Fout bij ophalen balances. Probeer later opnieuw."

class PriceCommand(CommandHandler):
    """Price command handler"""

    def __init__(self):
        super().__init__(
            name="price",
            description="Toon prijs van een cryptocurrency",
            admin_only=False
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle price command"""
        if not args:
            return "❌ Gebruik: /price <symbol>\nVoorbeeld: /price BTC/USDT"

        symbol = args[0].upper()

        try:
            # This would integrate with the exchange manager
            # For now, return a placeholder
            return f"""
📊 **Prijzen voor {symbol}**

**KUCOIN:**
• Last: $43,250.00
• Bid: $43,245.00
• Ask: $43,255.00
• 24h High: $44,100.00
• 24h Low: $42,800.00

**MEXC:**
• Last: $43,248.00
• Bid: $43,243.00
• Ask: $43,253.00
• 24h High: $44,095.00
• 24h Low: $42,795.00

📈 **24h Change:** *****%
📊 **Volume:** $1.2B
            """
        except Exception as e:
            logger.error(f"Error in price command: {e}")
            return f"❌ Fout bij ophalen prijs voor {symbol}. Probeer later opnieuw."

class AnalysisCommand(CommandHandler):
    """Analysis command handler"""

    def __init__(self):
        super().__init__(
            name="analysis",
            description="Toon marktanalyse",
            admin_only=True
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle analysis command"""
        try:
            # This would integrate with the market analyzer
            # For now, return a placeholder
            return """
📈 **Marktanalyse**

📊 **BTC/USDT**
• Prijs: $43,250.00
• 24h: *****%
• Trend: Bullish 📈
• RSI: 65 (Neutral)
• MACD: Bullish Cross
• Signaal: BUY 🟢

📊 **ETH/USDT**
• Prijs: $2,650.00
• 24h: *****%
• Trend: Bullish 📈
• RSI: 58 (Neutral)
• MACD: Bullish
• Signaal: BUY 🟢

📊 **BNB/USDT**
• Prijs: $315.50
• 24h: *****%
• Trend: Sideways ➡️
• RSI: 52 (Neutral)
• MACD: Neutral
• Signaal: HOLD 🟡

🤖 **AI Sentiment:** Bullish (75%)
📊 **Market Fear & Greed:** 68 (Greed)
            """
        except Exception as e:
            logger.error(f"Error in analysis command: {e}")
            return "❌ Fout bij ophalen marktanalyse. Probeer later opnieuw."

class TradingCommand(CommandHandler):
    """Trading command handler"""

    def __init__(self):
        super().__init__(
            name="trading",
            description="Toon trading dashboard",
            admin_only=True
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle trading command"""
        try:
            # This would integrate with the strategy manager
            # For now, return a placeholder
            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "🚀 Start Trading", "callback_data": "start_trading"},
                        {"text": "🛑 Stop Trading", "callback_data": "stop_trading"}
                    ],
                    [
                        {"text": "📊 View Positions", "callback_data": "view_positions"},
                        {"text": "📈 Performance", "callback_data": "view_performance"}
                    ],
                    [
                        {"text": "⚙️ Settings", "callback_data": "trading_settings"},
                        {"text": "🔙 Main Menu", "callback_data": "main_menu"}
                    ]
                ]
            }

            message_text = """
🤖 **Trading Dashboard**

**Status:** ⏸️ Gestopt
**Actieve Strategieën:** 0/4
**Open Posities:** 0
**Dagelijkse P&L:** $0.00

**Beschikbare Strategieën:**
• 📈 Day Trading (Inactief)
• ⚡ Scalping (Inactief)
• 🚀 Momentum (Inactief)
• 📊 Mean Reversion (Inactief)

**Risk Management:**
• Max Risk per Trade: 2%
• Max Daily Loss: 5%
• Max Open Positions: 5

Kies een actie hieronder:
            """

            await bot.send_message(message.chat_id, message_text, reply_markup=keyboard)
            return None  # Message already sent

        except Exception as e:
            logger.error(f"Error in trading command: {e}")
            return "❌ Fout bij laden trading dashboard. Probeer later opnieuw."

class PaperTradingCommand(CommandHandler):
    """Paper trading command handler"""

    def __init__(self):
        super().__init__(
            name="paper",
            description="Paper trading dashboard en commando's",
            admin_only=True
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle paper trading command"""
        try:
            from core.paper_trading import get_paper_trading_manager

            paper_manager = get_paper_trading_manager()

            if not args:
                # Show paper trading dashboard
                portfolio = paper_manager.get_portfolio_value()
                balances = paper_manager.get_balance()

                balance_text = ""
                for asset, balance in balances.items():
                    if balance['total'] > 0:
                        balance_text += f"• {asset}: {balance['total']:.6f}\n"

                keyboard = {
                    "inline_keyboard": [
                        [
                            {"text": "💰 Buy Order", "callback_data": "paper_buy"},
                            {"text": "📉 Sell Order", "callback_data": "paper_sell"}
                        ],
                        [
                            {"text": "📊 Portfolio", "callback_data": "paper_portfolio"},
                            {"text": "📈 Performance", "callback_data": "paper_performance"}
                        ],
                        [
                            {"text": "📋 Trade History", "callback_data": "paper_history"},
                            {"text": "🔄 Reset Portfolio", "callback_data": "paper_reset"}
                        ]
                    ]
                }

                message_text = f"""
📊 **PAPER TRADING DASHBOARD**

💰 **Portfolio Value:** ${portfolio['total_value']:.2f}
📈 **P&L:** ${portfolio['pnl']:.2f} ({portfolio['pnl_percentage']:.2f}%)
🎯 **Total Trades:** {portfolio['total_trades']}
🏆 **Win Rate:** {portfolio['win_rate']:.1f}%

💼 **Balances:**
{balance_text or "Geen balances"}

⚠️ **Paper Trading Mode** - Geen echt geld!
                """

                await bot.send_message(message.chat_id, message_text, reply_markup=keyboard)
                return None

            elif args[0] == "buy" and len(args) >= 3:
                # Paper buy order: /paper buy BTC/USDT 0.001
                symbol = args[1].upper()
                try:
                    amount = Decimal(args[2])
                    order = await paper_manager.create_order(symbol, "buy", amount)
                    if order:
                        return f"✅ Paper BUY order created:\n{amount} {symbol} @ ${order.price}"
                    else:
                        return "❌ Failed to create paper buy order"
                except ValueError:
                    return "❌ Invalid amount. Use: /paper buy BTC/USDT 0.001"

            elif args[0] == "sell" and len(args) >= 3:
                # Paper sell order: /paper sell BTC/USDT 0.001
                symbol = args[1].upper()
                try:
                    amount = Decimal(args[2])
                    order = await paper_manager.create_order(symbol, "sell", amount)
                    if order:
                        return f"✅ Paper SELL order created:\n{amount} {symbol} @ ${order.price}"
                    else:
                        return "❌ Failed to create paper sell order"
                except ValueError:
                    return "❌ Invalid amount. Use: /paper sell BTC/USDT 0.001"

            elif args[0] == "portfolio":
                # Show detailed portfolio
                portfolio = paper_manager.get_portfolio_value()
                balances = paper_manager.get_balance()

                balance_details = ""
                for asset, balance in balances.items():
                    if balance['total'] > 0:
                        balance_details += f"• {asset}: {balance['total']:.6f}\n"

                return f"""
📊 **PAPER TRADING PORTFOLIO**

💰 **Total Value:** ${portfolio['total_value']:.2f}
💵 **Initial Balance:** ${portfolio['initial_balance']:.2f}
📈 **P&L:** ${portfolio['pnl']:.2f}
📊 **P&L %:** {portfolio['pnl_percentage']:.2f}%

🎯 **Trading Stats:**
• Total Trades: {portfolio['total_trades']}
• Winning Trades: {portfolio['winning_trades']}
• Win Rate: {portfolio['win_rate']:.1f}%

💼 **Current Balances:**
{balance_details or "Geen balances"}

⚠️ **Paper Trading Mode** - Geen echt geld!
                """

            else:
                return """
📊 **Paper Trading Commands:**

**Dashboard:**
• `/paper` - Paper trading dashboard

**Trading:**
• `/paper buy BTC/USDT 0.001` - Paper buy order
• `/paper sell BTC/USDT 0.001` - Paper sell order
• `/paper portfolio` - Portfolio details

**Examples:**
• `/paper buy ETH/USDT 0.1`
• `/paper sell BTC/USDT 0.005`
                """

        except Exception as e:
            logger.error(f"Error in paper trading command: {e}")
            return "❌ Fout bij paper trading. Probeer later opnieuw."

class PerformanceCommand(CommandHandler):
    """Performance analysis command handler"""

    def __init__(self):
        super().__init__(
            name="performance",
            description="Geavanceerde performance analyse",
            admin_only=True
        )

    async def handle(self, message: TelegramMessage, args: List[str], bot) -> str:
        """Handle performance command"""
        try:
            from core.dashboard import get_dashboard

            dashboard = get_dashboard()

            if not args or args[0] == "summary":
                # Generate performance report
                report = dashboard.generate_performance_report()
                return report

            elif args[0] == "metrics":
                # Show advanced metrics
                metrics = dashboard.get_advanced_metrics()

                if "message" in metrics or "error" in metrics:
                    return "📊 Geen trading data beschikbaar voor metrics"

                return f"""
📊 **ADVANCED PERFORMANCE METRICS**

📈 **Risk & Return:**
• Sharpe Ratio: {metrics['sharpe_ratio']:.3f}
• Total Return: {metrics['total_return_percent']:.2f}%
• Max Drawdown: {metrics['max_drawdown_percent']:.2f}%
• Profit Factor: {metrics['profit_factor']:.2f}

🎯 **Trading Stats:**
• Total Trades: {metrics['total_trades']}
• Max Win Streak: {metrics['max_win_streak']}
• Max Loss Streak: {metrics['max_loss_streak']}
• Avg Trade Duration: {metrics['avg_trade_duration_hours']:.1f}h

📅 **Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """

            else:
                return """
📊 **Performance Commands:**

• `/performance` - Performance summary
• `/performance summary` - Detailed report
• `/performance metrics` - Advanced metrics

**Metrics Explained:**
• **Sharpe Ratio:** Risk-adjusted returns
• **Max Drawdown:** Largest peak-to-trough decline
• **Profit Factor:** Gross profit / Gross loss
                """

        except Exception as e:
            logger.error(f"Error in performance command: {e}")
            return "❌ Fout bij performance analyse. Probeer later opnieuw."
