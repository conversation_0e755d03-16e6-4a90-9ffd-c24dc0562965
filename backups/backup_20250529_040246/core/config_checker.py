"""
Configuration Checker for Trading Bot
Validates all settings and API connections
"""
import os
import asyncio
from typing import Dict, List, Tuple
from dotenv import load_dotenv
import ccxt
from loguru import logger

class Config<PERSON>hecker:
    """Check and validate bot configuration"""
    
    def __init__(self):
        load_dotenv()
        self.results = {
            'env_vars': {},
            'api_connections': {},
            'permissions': {},
            'warnings': [],
            'errors': []
        }
        
    async def check_all(self) -> Dict:
        """Run all configuration checks"""
        logger.info("Starting configuration check...")
        
        # Check environment variables
        self._check_env_vars()
        
        # Check API connections
        await self._check_api_connections()
        
        # Check file permissions
        self._check_permissions()
        
        # Generate summary
        self.results['summary'] = self._generate_summary()
        
        return self.results
        
    def _check_env_vars(self):
        """Check required environment variables"""
        required_vars = {
            'TELEGRAM_BOT_TOKEN': 'Telegram Bot Token',
            'TELEGRAM_ADMIN_USER_ID': 'Telegram Admin ID',
            'KUCOIN_API_KEY': 'KuCoin API Key',
            'KUCOIN_SECRET_KEY': 'KuCoin Secret Key',
            'KUCOIN_PASSPHRASE': 'KuCoin Passphrase',
            'MEXC_API_KEY': 'MEXC API Key',
            'MEXC_SECRET_KEY': 'MEXC Secret Key'
        }
        
        optional_vars = {
            'KUCOIN_SANDBOX': 'KuCoin Sandbox Mode',
            'MEXC_SANDBOX': 'MEXC Sandbox Mode',
            'NEWS_API_KEY': 'News API Key',
            'TWITTER_BEARER_TOKEN': 'Twitter Bearer Token',
            'OPENAI_API_KEY': 'OpenAI API Key'
        }
        
        # Check required vars
        for var, description in required_vars.items():
            value = os.getenv(var)
            if value:
                # Mask sensitive data
                if 'KEY' in var or 'SECRET' in var or 'TOKEN' in var:
                    masked = value[:4] + '*' * (len(value) - 8) + value[-4:]
                    self.results['env_vars'][var] = {'status': '✅', 'value': masked}
                else:
                    self.results['env_vars'][var] = {'status': '✅', 'value': value}
            else:
                self.results['env_vars'][var] = {'status': '❌', 'value': 'Missing'}
                self.results['errors'].append(f"{description} is missing")
                
        # Check optional vars
        for var, description in optional_vars.items():
            value = os.getenv(var)
            if value:
                if 'KEY' in var or 'SECRET' in var or 'TOKEN' in var:
                    masked = value[:4] + '*' * (len(value) - 8) + value[-4:]
                    self.results['env_vars'][var] = {'status': '✅', 'value': masked}
                else:
                    self.results['env_vars'][var] = {'status': '✅', 'value': value}
            else:
                self.results['env_vars'][var] = {'status': '⚠️', 'value': 'Not configured'}
                self.results['warnings'].append(f"{description} not configured (optional)")
                
    async def _check_api_connections(self):
        """Check API connections"""
        # Check KuCoin
        try:
            kucoin_sandbox = os.getenv('KUCOIN_SANDBOX', 'false').lower() == 'true'
            exchange_id = 'kucoinfutures' if kucoin_sandbox else 'kucoin'
            
            kucoin = ccxt.__dict__[exchange_id]({
                'apiKey': os.getenv('KUCOIN_API_KEY'),
                'secret': os.getenv('KUCOIN_SECRET_KEY'),
                'password': os.getenv('KUCOIN_PASSPHRASE'),
                'enableRateLimit': True
            })
            
            if kucoin_sandbox:
                kucoin.set_sandbox_mode(True)
                
            # Test connection
            balance = await kucoin.fetch_balance()
            self.results['api_connections']['KuCoin'] = {
                'status': '✅',
                'mode': 'Sandbox' if kucoin_sandbox else 'Live',
                'balance_check': 'Success'
            }
            await kucoin.close()
        except Exception as e:
            self.results['api_connections']['KuCoin'] = {
                'status': '❌',
                'error': str(e)[:100]
            }
            self.results['errors'].append(f"KuCoin connection failed: {str(e)[:50]}")
            
        # Check MEXC
        try:
            mexc_sandbox = os.getenv('MEXC_SANDBOX', 'false').lower() == 'true'
            
            mexc = ccxt.mexc({
                'apiKey': os.getenv('MEXC_API_KEY'),
                'secret': os.getenv('MEXC_SECRET_KEY'),
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot'
                }
            })
            
            if mexc_sandbox:
                mexc.set_sandbox_mode(True)
                
            # Test connection
            ticker = await mexc.fetch_ticker('BTC/USDT')
            self.results['api_connections']['MEXC'] = {
                'status': '✅',
                'mode': 'Sandbox' if mexc_sandbox else 'Live',
                'connection_check': 'Success'
            }
            await mexc.close()
        except Exception as e:
            self.results['api_connections']['MEXC'] = {
                'status': '❌',
                'error': str(e)[:100]
            }
            self.results['errors'].append(f"MEXC connection failed: {str(e)[:50]}")
            
    def _check_permissions(self):
        """Check file and directory permissions"""
        paths_to_check = [
            'logs/',
            'data/',
            '.env',
            'trading_bot.db'
        ]
        
        for path in paths_to_check:
            if os.path.exists(path):
                readable = os.access(path, os.R_OK)
                writable = os.access(path, os.W_OK)
                
                if readable and writable:
                    self.results['permissions'][path] = '✅ Read/Write'
                elif readable:
                    self.results['permissions'][path] = '⚠️ Read only'
                    self.results['warnings'].append(f"{path} is read-only")
                else:
                    self.results['permissions'][path] = '❌ No access'
                    self.results['errors'].append(f"No access to {path}")
            else:
                if path.endswith('/'):
                    # Directory should exist
                    self.results['permissions'][path] = '❌ Missing'
                    self.results['warnings'].append(f"Directory {path} does not exist")
                    
    def _generate_summary(self) -> Dict:
        """Generate configuration summary"""
        total_checks = len(self.results['env_vars']) + len(self.results['api_connections']) + len(self.results['permissions'])
        errors = len(self.results['errors'])
        warnings = len(self.results['warnings'])
        
        if errors == 0:
            status = '✅ Configuration OK'
        elif errors < 3:
            status = '⚠️ Configuration has issues'
        else:
            status = '❌ Configuration has serious problems'
            
        return {
            'status': status,
            'total_checks': total_checks,
            'errors': errors,
            'warnings': warnings
        }
        
    def get_report(self) -> str:
        """Generate human-readable configuration report"""
        report = """
🔧 **Configuration Check Report**

"""
        # Summary
        if 'summary' in self.results:
            summary = self.results['summary']
            report += f"**Status:** {summary['status']}\n"
            report += f"**Checks:** {summary['total_checks']}\n"
            report += f"**Errors:** {summary['errors']}\n"
            report += f"**Warnings:** {summary['warnings']}\n\n"
            
        # Environment Variables
        report += "**Environment Variables:**\n"
        for var, info in self.results['env_vars'].items():
            report += f"• {var}: {info['status']} {info.get('value', '')}\n"
            
        # API Connections
        report += "\n**API Connections:**\n"
        for api, info in self.results['api_connections'].items():
            report += f"• {api}: {info['status']}"
            if info['status'] == '✅':
                report += f" ({info.get('mode', 'Unknown')} mode)\n"
            else:
                report += f" - {info.get('error', 'Unknown error')}\n"
                
        # Permissions
        report += "\n**File Permissions:**\n"
        for path, status in self.results['permissions'].items():
            report += f"• {path}: {status}\n"
            
        # Errors and Warnings
        if self.results['errors']:
            report += "\n❌ **Errors:**\n"
            for error in self.results['errors']:
                report += f"• {error}\n"
                
        if self.results['warnings']:
            report += "\n⚠️ **Warnings:**\n"
            for warning in self.results['warnings']:
                report += f"• {warning}\n"
                
        return report

# Async function to run checks
async def check_configuration():
    """Run configuration check"""
    checker = ConfigChecker()
    await checker.check_all()
    return checker.get_report()
