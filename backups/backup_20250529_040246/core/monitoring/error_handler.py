"""
Enhanced <PERSON><PERSON><PERSON> for Trading Bot
Provides better error tracking and user notifications
"""
from typing import Optional, Dict, List
from datetime import datetime
from loguru import logger
import traceback
import asyncio
from collections import defaultdict

class ErrorHandler:
    """Advanced error handling with categorization and notifications"""
    
    def __init__(self):
        self.error_counts = defaultdict(int)
        self.error_history = []
        self.max_history = 100
        self.telegram_bot = None
        
        # Error categories
        self.error_categories = {
            'API_AUTH': '🔐 Authenticatie Probleem',
            'API_RATE': '⏱️ Rate Limit Bereikt',
            'NETWORK': '🌐 Netwerk Probleem',
            'EXCHANGE': '💱 Exchange Error',
            'STRATEGY': '🤖 Strategie Error',
            'DATA': '📊 Data Error',
            'UNKNOWN': '❓ Onbekende Error'
        }
        
    def set_telegram_bot(self, bot):
        """Set telegram bot for notifications"""
        self.telegram_bot = bot
        
    def categorize_error(self, error: Exception) -> str:
        """Categorize error based on type and message"""
        error_msg = str(error).lower()
        
        if '401' in error_msg or 'unauthorized' in error_msg or 'authentication' in error_msg:
            return 'API_AUTH'
        elif 'rate limit' in error_msg or '429' in error_msg:
            return 'API_RATE'
        elif 'connection' in error_msg or 'timeout' in error_msg or 'network' in error_msg:
            return 'NETWORK'
        elif 'exchange' in error_msg or 'ccxt' in error_msg:
            return 'EXCHANGE'
        elif 'strategy' in error_msg or 'position' in error_msg:
            return 'STRATEGY'
        elif 'data' in error_msg or 'parsing' in error_msg:
            return 'DATA'
        else:
            return 'UNKNOWN'
            
    async def handle_error(self, error: Exception, context: str = "", notify_user: bool = True) -> Dict:
        """Handle error with logging and optional user notification"""
        try:
            # Categorize error
            category = self.categorize_error(error)
            
            # Create error info
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'category': category,
                'context': context,
                'error_type': type(error).__name__,
                'error_message': str(error),
                'traceback': traceback.format_exc()
            }
            
            # Update counts
            self.error_counts[category] += 1
            
            # Add to history
            self.error_history.append(error_info)
            if len(self.error_history) > self.max_history:
                self.error_history.pop(0)
                
            # Log error
            logger.error(f"[{category}] {context}: {error}")
            
            # Notify user if needed
            if notify_user and self.telegram_bot:
                await self._notify_user(error_info)
                
            # Get suggested fix
            error_info['suggested_fix'] = self.get_suggested_fix(category, error)
            
            return error_info
            
        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return {'error': 'Failed to handle error'}
            
    def get_suggested_fix(self, category: str, error: Exception) -> str:
        """Get suggested fix for error category"""
        fixes = {
            'API_AUTH': """
🔧 **Mogelijke oplossingen:**
• Controleer je API keys in .env
• Verifieer dat API keys juist zijn
• Check of API permissies correct zijn
• Voor Twitter: genereer nieuwe bearer token
            """,
            'API_RATE': """
🔧 **Mogelijke oplossingen:**
• Wacht enkele minuten
• Verlaag update frequentie
• Gebruik minder API calls
            """,
            'NETWORK': """
🔧 **Mogelijke oplossingen:**
• Check internetverbinding
• Probeer opnieuw over 30 seconden
• Controleer firewall settings
            """,
            'EXCHANGE': """
🔧 **Mogelijke oplossingen:**
• Verifieer exchange status
• Check maintenance meldingen
• Controleer symbol/pair namen
            """,
            'STRATEGY': """
🔧 **Mogelijke oplossingen:**
• Check beschikbare balance
• Verifieer trading parameters
• Controleer position limits
            """,
            'DATA': """
🔧 **Mogelijke oplossingen:**
• Wacht op nieuwe data
• Check data format
• Verifieer timeframes
            """,
            'UNKNOWN': """
🔧 **Mogelijke oplossingen:**
• Herstart de bot
• Check logs voor details
• Contacteer support
            """
        }
        return fixes.get(category, fixes['UNKNOWN'])
        
    async def _notify_user(self, error_info: Dict):
        """Send error notification to user"""
        try:
            if not self.telegram_bot:
                return
                
            # Only notify for important errors
            if self.error_counts[error_info['category']] % 5 == 1:  # First and every 5th error
                message = f"""
⚠️ **Error Gedetecteerd**

**Type:** {self.error_categories.get(error_info['category'], 'Unknown')}
**Context:** {error_info['context']}
**Error:** {error_info['error_message'][:200]}...
**Aantal:** {self.error_counts[error_info['category']]}x

{error_info['suggested_fix']}
                """
                
                # Send to all admin users
                for admin_id in self.telegram_bot.admin_user_id:
                    await self.telegram_bot.send_message(admin_id, message)
                    
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
            
    def get_error_summary(self) -> str:
        """Get summary of recent errors"""
        if not self.error_history:
            return "✅ Geen recente errors"
            
        summary = "📊 **Error Overzicht**\n\n"
        
        # Count by category
        category_counts = defaultdict(int)
        for error in self.error_history[-20:]:  # Last 20 errors
            category_counts[error['category']] += 1
            
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            summary += f"{self.error_categories.get(category, category)}: {count}x\n"
            
        # Add recent errors
        summary += "\n**Recente Errors:**\n"
        for error in self.error_history[-5:]:
            timestamp = datetime.fromisoformat(error['timestamp']).strftime('%H:%M:%S')
            summary += f"• [{timestamp}] {error['context']}: {error['error_message'][:50]}...\n"
            
        return summary
        
    def should_pause_trading(self) -> bool:
        """Check if trading should be paused due to errors"""
        # Pause if too many critical errors
        critical_errors = self.error_counts['API_AUTH'] + self.error_counts['EXCHANGE']
        return critical_errors > 10
        
    def reset_error_counts(self):
        """Reset error counts (e.g., daily reset)"""
        self.error_counts.clear()
        logger.info("Error counts reset")

# Global error handler instance
error_handler = ErrorHandler()
