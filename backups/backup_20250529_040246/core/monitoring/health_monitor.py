"""
Health Monitoring System for Trading Bot
Tracks bot health and performance metrics
"""
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import asyncio
from loguru import logger

class HealthMonitor:
    """Monitor bot health and performance"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_check = datetime.now()
        
        # Health metrics
        self.metrics = {
            'api_calls': defaultdict(int),
            'trades_executed': 0,
            'trades_successful': 0,
            'trades_failed': 0,
            'errors_total': 0,
            'last_analysis': None,
            'last_trade': None,
            'exchange_status': {},
            'strategy_status': {},
            'system_status': 'starting'
        }
        
        # Performance tracking
        self.performance = {
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'avg_trade_duration': timedelta(0),
            'best_trade': None,
            'worst_trade': None
        }
        
    def update_api_call(self, exchange: str, endpoint: str):
        """Track API call"""
        self.metrics['api_calls'][f"{exchange}_{endpoint}"] += 1
        
    def update_trade(self, trade_info: Dict):
        """Update trade statistics"""
        self.metrics['trades_executed'] += 1
        self.metrics['last_trade'] = datetime.now()
        
        if trade_info.get('success', False):
            self.metrics['trades_successful'] += 1
        else:
            self.metrics['trades_failed'] += 1
            
        # Update performance
        if 'pnl' in trade_info:
            self.performance['total_pnl'] += trade_info['pnl']
            
            # Track best/worst
            if not self.performance['best_trade'] or trade_info['pnl'] > self.performance['best_trade']['pnl']:
                self.performance['best_trade'] = trade_info
            if not self.performance['worst_trade'] or trade_info['pnl'] < self.performance['worst_trade']['pnl']:
                self.performance['worst_trade'] = trade_info
                
        # Calculate win rate
        if self.metrics['trades_executed'] > 0:
            self.performance['win_rate'] = (self.metrics['trades_successful'] / self.metrics['trades_executed']) * 100
            
    def update_exchange_status(self, exchange: str, status: str, details: Dict = None):
        """Update exchange connection status"""
        self.metrics['exchange_status'][exchange] = {
            'status': status,
            'last_update': datetime.now(),
            'details': details or {}
        }
        
    def update_strategy_status(self, strategy: str, active: bool, positions: int = 0):
        """Update strategy status"""
        self.metrics['strategy_status'][strategy] = {
            'active': active,
            'positions': positions,
            'last_update': datetime.now()
        }
        
    def get_health_status(self) -> Dict:
        """Get overall health status"""
        now = datetime.now()
        uptime = now - self.start_time
        
        # Check various health indicators
        health_score = 100
        issues = []
        
        # Check if analysis is running
        if self.metrics['last_analysis']:
            time_since_analysis = now - self.metrics['last_analysis']
            if time_since_analysis > timedelta(minutes=10):
                health_score -= 20
                issues.append("⚠️ Marktanalyse loopt achter")
                
        # Check exchange connections
        for exchange, status in self.metrics['exchange_status'].items():
            if status['status'] != 'connected':
                health_score -= 30
                issues.append(f"❌ {exchange} niet verbonden")
                
        # Check error rate
        if self.metrics['trades_executed'] > 0:
            error_rate = (self.metrics['trades_failed'] / self.metrics['trades_executed']) * 100
            if error_rate > 20:
                health_score -= 20
                issues.append(f"⚠️ Hoge error rate: {error_rate:.1f}%")
                
        # Determine overall status
        if health_score >= 80:
            status = "🟢 Gezond"
        elif health_score >= 60:
            status = "🟡 Waarschuwing"
        else:
            status = "🔴 Kritiek"
            
        return {
            'status': status,
            'health_score': health_score,
            'uptime': str(uptime).split('.')[0],
            'issues': issues,
            'metrics': self.metrics,
            'performance': self.performance
        }
        
    def get_status_report(self) -> str:
        """Generate human-readable status report"""
        health = self.get_health_status()
        
        report = f"""
🏥 **Bot Health Status**

**Status:** {health['status']} ({health['health_score']}/100)
**Uptime:** {health['uptime']}

📊 **Trading Metrics:**
• Trades Uitgevoerd: {self.metrics['trades_executed']}
• Success Rate: {self.performance['win_rate']:.1f}%
• Totale P&L: ${self.performance['total_pnl']:.2f}

💱 **Exchange Status:**
"""
        
        for exchange, status in self.metrics['exchange_status'].items():
            emoji = "✅" if status['status'] == 'connected' else "❌"
            report += f"• {exchange}: {emoji} {status['status']}\n"
            
        report += "\n🤖 **Strategie Status:**\n"
        
        for strategy, status in self.metrics['strategy_status'].items():
            emoji = "🟢" if status['active'] else "🔴"
            report += f"• {strategy}: {emoji} {'Actief' if status['active'] else 'Inactief'} ({status['positions']} posities)\n"
            
        if health['issues']:
            report += "\n⚠️ **Problemen:**\n"
            for issue in health['issues']:
                report += f"• {issue}\n"
                
        return report

# Global health monitor instance
health_monitor = HealthMonitor()
