"""
Verbeterde Trade Manager voor het beheren van alle trading activiteiten
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal
from dataclasses import dataclass, field
from loguru import logger
from core.logging_config import log_trade
from core.dashboard import get_dashboard, TradeRecord

@dataclass
class TradeInfo:
    """Trade informatie data structure"""
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: Decimal
    price: Decimal
    exchange: str
    strategy: str
    timestamp: datetime = field(default_factory=datetime.now)
    order_id: str = ""
    status: str = "pending"
    fee: Decimal = Decimal('0')
    pnl: Optional[Decimal] = None

class TradeManager:
    """
    Verbeterde Trade Manager voor het beheren van alle trading activiteiten

    Deze klasse beheert trade execution, tracking, en rapportage
    met integratie naar dashboard en monitoring systemen.
    """

    def __init__(self, exchange_manager=None, strategy_manager=None, risk_manager=None):
        self.exchange_manager = exchange_manager
        self.strategy_manager = strategy_manager
        self.risk_manager = risk_manager

        # Trade tracking
        self.active_trades: Dict[str, TradeInfo] = {}
        self.completed_trades: List[TradeInfo] = []
        self.daily_trades: List[TradeInfo] = []

        # Statistics
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.total_pnl = Decimal('0')

        # Last trade info voor heartbeat
        self.last_trade_info: Optional[TradeInfo] = None

        logger.info("✅ Trade Manager initialized")

    async def execute_trade(self, symbol: str, side: str, amount: Decimal,
                           strategy_name: str, price: Optional[Decimal] = None) -> Dict[str, Any]:
        """
        Voer een trade uit met volledige risk management en logging

        Args:
            symbol: Trading pair (e.g., "BTC/USDT")
            side: "buy" or "sell"
            amount: Amount to trade
            strategy_name: Name of the strategy
            price: Optional limit price

        Returns:
            Dictionary met trade resultaat
        """
        try:
            # 1. Risk management check
            if self.risk_manager:
                risk_check = await self.risk_manager.validate_new_trade(
                    symbol, side, amount, price or Decimal('0')
                )
                if not risk_check[0]:
                    logger.warning(f"❌ Trade rejected by risk manager: {risk_check[1]}")
                    return {"status": "rejected", "reason": risk_check[1]}

            # 2. Determine best exchange and price
            if not price and self.exchange_manager:
                best_result = await self.exchange_manager.find_best_price(symbol, side)
                if best_result:
                    exchange, best_price = best_result
                    price = Decimal(str(best_price))
                else:
                    return {"status": "failed", "reason": "No exchange available"}
            else:
                exchange = "kucoin"  # Default exchange

            # 3. Create trade info
            trade_info = TradeInfo(
                symbol=symbol,
                side=side,
                amount=amount,
                price=price,
                exchange=exchange,
                strategy=strategy_name
            )

            # 4. Execute trade
            if self.exchange_manager:
                order_result = await self.exchange_manager.create_order(
                    exchange_name=exchange,
                    order_type="market",
                    side=side,
                    symbol=symbol,
                    amount=amount,
                    price=price if side == "limit" else None
                )

                if order_result:
                    trade_info.order_id = order_result.id
                    trade_info.status = "executed"
                    self.successful_trades += 1
                else:
                    trade_info.status = "failed"
                    self.failed_trades += 1
            else:
                # Simulation mode
                trade_info.status = "simulated"
                trade_info.order_id = f"SIM_{int(time.time())}"
                self.successful_trades += 1

            # 5. Update tracking
            self.total_trades += 1
            self.last_trade_info = trade_info
            self.daily_trades.append(trade_info)

            # 6. Add to dashboard
            dashboard = get_dashboard()
            trade_record = TradeRecord(
                timestamp=trade_info.timestamp,
                symbol=trade_info.symbol,
                side=trade_info.side,
                amount=trade_info.amount,
                price=trade_info.price,
                fee=trade_info.fee,
                exchange=trade_info.exchange,
                strategy=trade_info.strategy,
                order_id=trade_info.order_id
            )
            dashboard.add_trade(trade_record)

            # 7. Log trade
            log_trade(
                action=side,
                symbol=symbol,
                amount=float(amount),
                price=float(price),
                exchange=exchange,
                order_id=trade_info.order_id,
                status=trade_info.status
            )

            logger.info(f"✅ Trade executed: {side.upper()} {amount} {symbol} @ {price}")

            return {
                "status": "success",
                "trade_info": trade_info,
                "order_id": trade_info.order_id
            }

        except Exception as e:
            logger.error(f"❌ Error executing trade: {e}")
            self.failed_trades += 1
            return {"status": "error", "reason": str(e)}

    def get_last_trade_info(self) -> Optional[Dict[str, Any]]:
        """Haal laatste trade informatie op voor heartbeat"""
        if not self.last_trade_info:
            return None

        return {
            "symbol": self.last_trade_info.symbol,
            "side": self.last_trade_info.side,
            "amount": str(self.last_trade_info.amount),
            "price": str(self.last_trade_info.price),
            "exchange": self.last_trade_info.exchange,
            "strategy": self.last_trade_info.strategy,
            "timestamp": self.last_trade_info.timestamp.strftime('%H:%M:%S'),
            "status": self.last_trade_info.status
        }

    def get_daily_trade_count(self) -> int:
        """Haal aantal trades vandaag op"""
        today = datetime.now().date()
        return len([t for t in self.daily_trades if t.timestamp.date() == today])

    def get_trade_statistics(self) -> Dict[str, Any]:
        """Haal trade statistieken op"""
        win_rate = (self.successful_trades / self.total_trades * 100) if self.total_trades > 0 else 0

        return {
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "failed_trades": self.failed_trades,
            "win_rate": win_rate,
            "daily_trades": self.get_daily_trade_count(),
            "total_pnl": float(self.total_pnl)
        }

    def reset_daily_stats(self):
        """Reset dagelijkse statistieken (wordt aangeroepen om middernacht)"""
        today = datetime.now().date()
        self.daily_trades = [t for t in self.daily_trades if t.timestamp.date() == today]
        logger.info("📊 Daily trade stats reset")

# Global trade manager instance
_trade_manager = None

def get_trade_manager() -> TradeManager:
    """Get global trade manager instance"""
    global _trade_manager
    if _trade_manager is None:
        _trade_manager = TradeManager()
    return _trade_manager
