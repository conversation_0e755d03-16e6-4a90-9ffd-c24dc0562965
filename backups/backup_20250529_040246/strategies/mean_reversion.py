"""
Mean Reversion Strategy - Trading oversold/overbought conditions
"""
import asyncio
from decimal import Decimal
from typing import Dict, List
# import numpy as np  # Disabled for Python 3.13 compatibility
from .base import BaseStrategy, TradingSignal, SignalType, Position, StrategyConfig
from analysis.technical_simple import TechnicalAnalyzer
from analysis.ai_analyzer import AIAnalyzer
from loguru import logger

class MeanReversionStrategy(BaseStrategy):
    """
    Mean reversion strategy that trades when prices deviate significantly from their mean

    Features:
    - Bollinger Bands analysis
    - RSI divergence detection
    - Statistical mean reversion
    - Volume profile analysis
    - Multi-timeframe confirmation
    - Dynamic position sizing based on deviation
    """

    def __init__(self, config: StrategyConfig):
        super().__init__(config)
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_analyzer = AIAnalyzer()

        # Mean reversion specific settings
        self.bb_deviation_threshold = 2.0  # Bollinger Band standard deviations
        self.rsi_oversold = 25  # RSI oversold level
        self.rsi_overbought = 75  # RSI overbought level
        self.mean_lookback = 50  # Periods for mean calculation
        self.volume_confirmation_threshold = 0.8  # Volume confirmation level
        self.max_deviation_percentage = 15  # Maximum % deviation from mean

    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """Analyze market for mean reversion opportunities"""
        try:
            # Multi-timeframe analysis
            tf_1h = await self.technical_analyzer.analyze(symbol, market_data, "1h")
            tf_4h = await self.technical_analyzer.analyze(symbol, market_data, "4h")

            # AI sentiment analysis
            ai_analysis = await self.ai_analyzer.analyze_sentiment(symbol, market_data)

            current_price = Decimal(str(market_data['price']))

            # Bollinger Bands analysis
            bb_signal = await self._analyze_bollinger_bands(tf_1h, current_price)

            # RSI analysis with divergence
            rsi_signal = await self._analyze_rsi_divergence(tf_1h, market_data)

            # Statistical mean reversion
            mean_reversion_signal = await self._calculate_mean_reversion(market_data)

            # Volume profile analysis
            volume_signal = await self._analyze_volume_profile(market_data)

            # Support/Resistance confluence
            sr_signal = await self._analyze_support_resistance_confluence(market_data, tf_4h)

            # Combine all signals
            final_score = self._combine_mean_reversion_signals(
                bb_signal, rsi_signal, mean_reversion_signal,
                volume_signal, sr_signal, ai_analysis
            )

            # Generate trading signal
            if final_score > self.config.min_confidence:
                signal_type = SignalType.BUY
                confidence = float(final_score)

                # Mean reversion targets
                mean_price = await self._calculate_dynamic_mean(market_data)
                stop_loss = current_price * (1 - self.config.stop_loss_percentage / 100)
                take_profit = mean_price  # Target mean price

                reasoning = f"Mean reversion BUY: BB={bb_signal:.2f}, RSI={rsi_signal:.2f}, Mean={mean_reversion_signal:.2f}, Volume={volume_signal:.2f}"

            elif final_score < -self.config.min_confidence:
                signal_type = SignalType.SELL
                confidence = float(abs(final_score))

                mean_price = await self._calculate_dynamic_mean(market_data)
                stop_loss = current_price * (1 + self.config.stop_loss_percentage / 100)
                take_profit = mean_price

                reasoning = f"Mean reversion SELL: BB={bb_signal:.2f}, RSI={rsi_signal:.2f}, Mean={mean_reversion_signal:.2f}"

            else:
                signal_type = SignalType.HOLD
                confidence = 0.5
                stop_loss = None
                take_profit = None
                reasoning = "No clear mean reversion opportunity"

            return TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=reasoning
            )

        except Exception as e:
            logger.error(f"Error in mean reversion analysis for {symbol}: {e}")
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=Decimal(str(market_data['price'])),
                reasoning=f"Mean reversion analysis error: {str(e)}"
            )

    async def _analyze_bollinger_bands(self, technical_data: Dict, current_price: Decimal) -> float:
        """Analyze Bollinger Bands for mean reversion signals"""
        try:
            bb_upper = technical_data.get('bb_upper', 0)
            bb_lower = technical_data.get('bb_lower', 0)
            bb_middle = technical_data.get('bb_middle', 0)

            if bb_upper == 0 or bb_lower == 0:
                return 0.0

            current_price_float = float(current_price)

            # Calculate position within bands
            if bb_upper > bb_lower:
                band_width = bb_upper - bb_lower
                position_in_band = (current_price_float - bb_lower) / band_width

                # Strong signals at band extremes
                if current_price_float <= bb_lower:
                    return 0.8  # Strong buy signal (oversold)
                elif current_price_float >= bb_upper:
                    return -0.8  # Strong sell signal (overbought)
                elif position_in_band < 0.2:
                    return 0.5  # Moderate buy signal
                elif position_in_band > 0.8:
                    return -0.5  # Moderate sell signal
                else:
                    return 0.0  # Neutral

            return 0.0

        except Exception:
            return 0.0

    async def _analyze_rsi_divergence(self, technical_data: Dict, market_data: Dict) -> float:
        """Analyze RSI with divergence detection"""
        try:
            rsi = technical_data.get('rsi', 50)
            rsi_previous = technical_data.get('rsi_previous', rsi)

            current_price = market_data['price']
            previous_price = market_data.get('previous_price', current_price)

            signal = 0.0

            # Basic RSI signals
            if rsi <= self.rsi_oversold:
                signal += 0.6  # Oversold
            elif rsi >= self.rsi_overbought:
                signal -= 0.6  # Overbought

            # RSI divergence detection
            price_change = (current_price - previous_price) / previous_price
            rsi_change = rsi - rsi_previous

            # Bullish divergence: price down, RSI up
            if price_change < -0.02 and rsi_change > 2:
                signal += 0.4

            # Bearish divergence: price up, RSI down
            elif price_change > 0.02 and rsi_change < -2:
                signal -= 0.4

            return max(-1.0, min(1.0, signal))

        except Exception:
            return 0.0

    async def _calculate_mean_reversion(self, market_data: Dict) -> float:
        """Calculate statistical mean reversion signal"""
        try:
            current_price = market_data['price']

            # Get historical prices (simplified)
            sma_20 = market_data.get('sma_20', current_price)
            sma_50 = market_data.get('sma_50', current_price)

            # Calculate deviation from mean
            mean_price = (sma_20 + sma_50) / 2
            deviation = (current_price - mean_price) / mean_price * 100

            # Strong mean reversion signals
            if deviation < -self.max_deviation_percentage:
                return 0.9  # Extremely oversold
            elif deviation > self.max_deviation_percentage:
                return -0.9  # Extremely overbought
            elif deviation < -5:
                return 0.6  # Oversold
            elif deviation > 5:
                return -0.6  # Overbought
            else:
                return 0.0  # Near mean

        except Exception:
            return 0.0

    async def _analyze_volume_profile(self, market_data: Dict) -> float:
        """Analyze volume profile for mean reversion confirmation"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_20', current_volume)

            if avg_volume == 0:
                return 0.0

            volume_ratio = current_volume / avg_volume

            # High volume on extreme moves = stronger mean reversion signal
            if volume_ratio > 1.5:
                return 0.3  # Volume confirmation
            elif volume_ratio < 0.5:
                return -0.2  # Low volume = weaker signal
            else:
                return 0.0

        except Exception:
            return 0.0

    async def _analyze_support_resistance_confluence(self, market_data: Dict, tf_4h: Dict) -> float:
        """Analyze support/resistance confluence for mean reversion"""
        try:
            current_price = Decimal(str(market_data['price']))

            # Key levels
            resistance_levels = [
                market_data.get('high_24h', 0),
                tf_4h.get('resistance_1', 0),
                tf_4h.get('resistance_2', 0)
            ]

            support_levels = [
                market_data.get('low_24h', 0),
                tf_4h.get('support_1', 0),
                tf_4h.get('support_2', 0)
            ]

            # Check proximity to support/resistance
            signal = 0.0

            for level in support_levels:
                if level > 0:
                    distance = abs(current_price - Decimal(str(level))) / current_price
                    if distance < 0.02:  # Within 2%
                        signal += 0.3  # Near support = bullish for mean reversion

            for level in resistance_levels:
                if level > 0:
                    distance = abs(current_price - Decimal(str(level))) / current_price
                    if distance < 0.02:  # Within 2%
                        signal -= 0.3  # Near resistance = bearish for mean reversion

            return max(-1.0, min(1.0, signal))

        except Exception:
            return 0.0

    async def _calculate_dynamic_mean(self, market_data: Dict) -> Decimal:
        """Calculate dynamic mean price target"""
        try:
            # Use multiple moving averages for dynamic mean
            sma_20 = market_data.get('sma_20', market_data['price'])
            sma_50 = market_data.get('sma_50', market_data['price'])
            ema_21 = market_data.get('ema_21', market_data['price'])

            # Weighted average of different means
            dynamic_mean = (sma_20 * 0.4 + sma_50 * 0.3 + ema_21 * 0.3)

            return Decimal(str(dynamic_mean))

        except Exception:
            return Decimal(str(market_data['price']))

    def _combine_mean_reversion_signals(self, bb_signal: float, rsi_signal: float,
                                       mean_signal: float, volume_signal: float,
                                       sr_signal: float, ai_analysis: Dict) -> float:
        """Combine all mean reversion signals"""

        weights = {
            'bollinger': 0.25,
            'rsi': 0.25,
            'mean': 0.25,
            'volume': 0.1,
            'support_resistance': 0.1,
            'ai': 0.05
        }

        # Get AI sentiment score
        ai_score = (ai_analysis.get('sentiment_score', 0.5) - 0.5) * 2

        # Combine weighted signals
        combined = (
            weights['bollinger'] * bb_signal +
            weights['rsi'] * rsi_signal +
            weights['mean'] * mean_signal +
            weights['volume'] * volume_signal +
            weights['support_resistance'] * sr_signal +
            weights['ai'] * ai_score
        )

        return max(-1.0, min(1.0, combined))

    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """Determine if we should enter a mean reversion position"""

        # Mean reversion requires high confidence
        if signal.confidence < float(self.config.min_confidence):
            return False

        # Limit positions
        if len(self.positions) >= self.config.max_positions:
            return False

        # Only enter on clear signals
        if signal.signal in [SignalType.BUY, SignalType.SELL]:
            return True

        return False

    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """Determine if we should exit a mean reversion position"""

        # Check if price has reverted to mean (take profit)
        if position.take_profit:
            if position.side == 'long' and current_price >= position.take_profit:
                return True
            elif position.side == 'short' and current_price <= position.take_profit:
                return True

        # Check stop loss
        if position.stop_loss:
            if position.side == 'long' and current_price <= position.stop_loss:
                return True
            elif position.side == 'short' and current_price >= position.stop_loss:
                return True

        # Exit if trend continues against mean reversion expectation
        pnl, pnl_percentage = await self.calculate_pnl(position, current_price)

        # If loss exceeds 5%, consider exiting (trend might be stronger than expected)
        if pnl_percentage < -5:
            return True

        return False

    async def calculate_position_size(self, signal: TradingSignal, portfolio_value: Decimal) -> Decimal:
        """Calculate position size based on deviation magnitude"""
        base_size = await super().calculate_position_size(signal, portfolio_value)

        # Increase position size for stronger mean reversion signals
        if signal.confidence > 0.8:
            return base_size * Decimal('1.5')  # 50% larger position
        elif signal.confidence > 0.9:
            return base_size * Decimal('2.0')  # Double position

        return base_size
