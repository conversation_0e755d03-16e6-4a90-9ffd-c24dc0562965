"""
Strategy Manager - Manages all trading strategies
"""
import asyncio
import time
from typing import Dict, List, Optional
from decimal import Decimal
from loguru import logger
from .base import BaseStrategy, StrategyConfig, TradingSignal, Position
from .daytrading import DayTradingStrategy
from .scalping import ScalpingStrategy
from .momentum_breakout import MomentumBreakoutStrategy
from .mean_reversion import MeanReversionStrategy
from exchanges.manager import ExchangeManager

class StrategyManager:
    """Manages all trading strategies and their execution"""

    def __init__(self, exchange_manager: ExchangeManager, risk_manager=None):
        self.exchange_manager = exchange_manager
        self.risk_manager = risk_manager
        self.strategies: Dict[str, BaseStrategy] = {}
        self.active_positions: Dict[str, Position] = {}
        self.running = False
        self.last_analysis_time = 0
        self.analysis_interval = 300  # 5 minutes

        # Initialize strategies
        self._initialize_strategies()

    def _initialize_strategies(self):
        """Initialize all trading strategies"""

        # Day Trading Strategy
        daytrading_config = StrategyConfig(
            name="Day Trading",
            enabled=True,
            risk_percentage=Decimal('2'),
            max_positions=3,
            stop_loss_percentage=Decimal('2'),
            take_profit_percentage=Decimal('6'),
            trailing_stop_percentage=Decimal('1'),
            min_confidence=Decimal('0.5'),  # Lowered for more trades
            timeframe="15m",
            symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        )
        self.strategies['daytrading'] = DayTradingStrategy(daytrading_config)

        # Scalping Strategy
        scalping_config = StrategyConfig(
            name="Scalping",
            enabled=True,
            risk_percentage=Decimal('1'),
            max_positions=2,
            stop_loss_percentage=Decimal('0.5'),
            take_profit_percentage=Decimal('1'),
            trailing_stop_percentage=Decimal('0.2'),
            min_confidence=Decimal('0.8'),
            timeframe="1m",
            symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT"]
        )
        self.strategies['scalping'] = ScalpingStrategy(scalping_config)

        # Momentum Breakout Strategy
        momentum_config = StrategyConfig(
            name="Momentum Breakout",
            enabled=True,
            risk_percentage=Decimal('3'),
            max_positions=2,
            stop_loss_percentage=Decimal('3'),
            take_profit_percentage=Decimal('9'),
            trailing_stop_percentage=Decimal('1.5'),
            min_confidence=Decimal('0.75'),
            timeframe="1h",
            symbols=["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
        )
        self.strategies['momentum'] = MomentumBreakoutStrategy(momentum_config)

        # Mean Reversion Strategy
        mean_reversion_config = StrategyConfig(
            name="Mean Reversion",
            enabled=True,
            risk_percentage=Decimal('2.5'),
            max_positions=3,
            stop_loss_percentage=Decimal('4'),
            take_profit_percentage=Decimal('6'),
            trailing_stop_percentage=Decimal('1'),
            min_confidence=Decimal('0.7'),
            timeframe="4h",
            symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT"]
        )
        self.strategies['mean_reversion'] = MeanReversionStrategy(mean_reversion_config)

        logger.info(f"✅ Initialized {len(self.strategies)} trading strategies")

    async def start_automated_trading(self):
        """Start automated trading with all strategies"""
        self.running = True
        logger.info("🤖 Starting automated trading with all strategies")

        # Force immediate trades for testing
        await self._force_immediate_trades()

        # Start strategy execution loops
        tasks = []
        for strategy_name, strategy in self.strategies.items():
            if strategy.config.enabled:
                task = asyncio.create_task(self._run_strategy(strategy_name, strategy))
                tasks.append(task)

        # Wait for all strategy tasks
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in automated trading: {e}")
        finally:
            self.running = False

    def stop_automated_trading(self):
        """Stop automated trading"""
        self.running = False
        logger.info("🛑 Stopping automated trading")

    async def _run_strategy(self, strategy_name: str, strategy: BaseStrategy):
        """Run a single strategy in a loop"""
        logger.info(f"🚀 Starting {strategy_name} strategy")

        while self.running and strategy.active:
            try:
                # Get portfolio value
                portfolio_value = await self._get_portfolio_value()

                # Analyze each symbol for this strategy
                for symbol in strategy.config.symbols:
                    await self._execute_strategy_for_symbol(strategy_name, strategy, symbol, portfolio_value)

                # Update existing positions
                await self._update_positions(strategy_name, strategy)

                # Wait based on strategy timeframe
                sleep_time = self._get_sleep_time(strategy.config.timeframe)
                await asyncio.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in {strategy_name} strategy: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def _execute_strategy_for_symbol(self, strategy_name: str, strategy: BaseStrategy,
                                         symbol: str, portfolio_value: Decimal):
        """Execute strategy analysis and trading for a specific symbol"""
        try:
            # Get market data
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                return

            # Use best ticker
            best_ticker = max(tickers.values(), key=lambda t: t.volume)
            market_data = {
                'price': float(best_ticker.last),
                'bid': float(best_ticker.bid),
                'ask': float(best_ticker.ask),
                'high_24h': float(best_ticker.high),
                'low_24h': float(best_ticker.low),
                'volume': float(best_ticker.volume),
                'timestamp': best_ticker.timestamp
            }

            # Analyze with strategy
            signal = await strategy.analyze(symbol, market_data)

            # Check if we should enter a position
            if await strategy.should_enter_position(signal, portfolio_value):
                await self._enter_position(strategy_name, strategy, signal, symbol, portfolio_value)

        except Exception as e:
            logger.error(f"Error executing {strategy_name} for {symbol}: {e}")

    async def _enter_position(self, strategy_name: str, strategy: BaseStrategy,
                            signal: TradingSignal, symbol: str, portfolio_value: Decimal):
        """Enter a new trading position with risk management"""
        try:
            # Calculate position size
            position_size = await strategy.calculate_position_size(signal, portfolio_value)

            if position_size <= 0:
                return

            # Determine order type and side
            order_type = "limit" if signal.take_profit else "market"
            side = "buy" if signal.signal.value == "buy" else "sell"

            # Risk management validation
            if self.risk_manager:
                valid, reason = await self.risk_manager.validate_new_trade(
                    symbol, side, position_size, signal.price, signal.stop_loss
                )
                if not valid:
                    logger.warning(f"Trade rejected by risk manager: {reason}")
                    return

            # Find best exchange for this trade
            best_exchange_result = await self.exchange_manager.find_best_price(symbol, side)
            if not best_exchange_result:
                logger.warning(f"No exchange available for {symbol}")
                return

            best_exchange, best_price = best_exchange_result

            # Create order
            order = await self.exchange_manager.create_order(
                exchange_name=best_exchange,
                order_type=order_type,
                side=side,
                symbol=symbol,
                amount=position_size,
                price=signal.price if order_type == "limit" else None
            )

            # Create position record
            position = Position(
                symbol=symbol,
                side="long" if side == "buy" else "short",
                entry_price=signal.price,
                amount=position_size,
                current_price=signal.price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                trailing_stop=strategy.config.trailing_stop_percentage,
                timestamp=int(time.time())
            )

            # Store position
            position_key = f"{strategy_name}_{symbol}_{order.id}"
            self.active_positions[position_key] = position
            strategy.positions[position_key] = position

            # Add to risk manager tracking
            if self.risk_manager:
                await self.risk_manager.add_position(
                    symbol, side, position_size, signal.price, signal.stop_loss
                )

            logger.info(f"✅ {strategy_name}: Entered {side} position for {symbol} at ${signal.price}")
            logger.info(f"   Order ID: {order.id}, Size: {position_size}, Exchange: {best_exchange}")

        except Exception as e:
            logger.error(f"Error entering position for {strategy_name}: {e}")

    async def _update_positions(self, strategy_name: str, strategy: BaseStrategy):
        """Update existing positions for a strategy"""
        try:
            positions_to_close = []

            for position_key, position in strategy.positions.items():
                # Get current price
                tickers = await self.exchange_manager.get_ticker_from_all(position.symbol)
                if not tickers:
                    continue

                best_ticker = max(tickers.values(), key=lambda t: t.volume)
                current_price = Decimal(str(best_ticker.last))

                # Update position
                position.current_price = current_price
                position.pnl, position.pnl_percentage = await strategy.calculate_pnl(position, current_price)

                # Update risk manager with current price
                if self.risk_manager:
                    await self.risk_manager.update_position(position.symbol, current_price)

                # Check if we should exit
                if await strategy.should_exit_position(position, current_price):
                    positions_to_close.append(position_key)

            # Close positions that need to be closed
            for position_key in positions_to_close:
                await self._close_position(strategy_name, strategy, position_key)

        except Exception as e:
            logger.error(f"Error updating positions for {strategy_name}: {e}")

    async def _close_position(self, strategy_name: str, strategy: BaseStrategy, position_key: str):
        """Close a trading position"""
        try:
            position = strategy.positions.get(position_key)
            if not position:
                return

            # Determine exit side
            exit_side = "sell" if position.side == "long" else "buy"

            # Find best exchange for exit
            best_exchange_result = await self.exchange_manager.find_best_price(position.symbol, exit_side)
            if not best_exchange_result:
                logger.warning(f"No exchange available for closing {position.symbol}")
                return

            best_exchange, best_price = best_exchange_result

            # Create exit order
            order = await self.exchange_manager.create_order(
                exchange_name=best_exchange,
                order_type="market",
                side=exit_side,
                symbol=position.symbol,
                amount=position.amount
            )

            # Calculate final PnL
            final_pnl, final_pnl_percentage = await strategy.calculate_pnl(position, position.current_price)

            # Remove from risk manager tracking
            if self.risk_manager:
                realized_pnl = await self.risk_manager.remove_position(position.symbol, position.side)

            # Remove position
            del strategy.positions[position_key]
            if position_key in self.active_positions:
                del self.active_positions[position_key]

            logger.info(f"✅ {strategy_name}: Closed {position.side} position for {position.symbol}")
            logger.info(f"   PnL: ${final_pnl:.2f} ({final_pnl_percentage:+.2f}%)")

        except Exception as e:
            logger.error(f"Error closing position {position_key}: {e}")

    async def _get_portfolio_value(self) -> Decimal:
        """Get total portfolio value across all exchanges"""
        try:
            total_value = Decimal('0')
            all_balances = await self.exchange_manager.get_all_balances()

            for exchange_name, balances in all_balances.items():
                for currency, balance in balances.items():
                    if currency == 'USDT':
                        total_value += balance.total
                    elif balance.total > 0:
                        # Convert to USDT (simplified) - only for major cryptocurrencies
                        major_cryptos = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'LTC', 'XRP']
                        if currency in major_cryptos:
                            try:
                                symbol = f"{currency}/USDT"
                                ticker = await self.exchange_manager.get_exchange(exchange_name).get_ticker(symbol)
                                total_value += balance.total * ticker.last
                            except Exception as e:
                                logger.debug(f"Could not convert {currency} to USDT: {e}")
                                pass  # Skip if can't convert

            return total_value

        except Exception as e:
            logger.error(f"Error calculating portfolio value: {e}")
            return Decimal('1000')  # Default fallback

    def _get_sleep_time(self, timeframe: str) -> int:
        """Get sleep time based on timeframe"""
        timeframe_sleep = {
            "1m": 60,      # 1 minute
            "5m": 300,     # 5 minutes
            "15m": 900,    # 15 minutes
            "1h": 3600,    # 1 hour
            "4h": 14400,   # 4 hours
            "1d": 86400    # 1 day
        }
        return timeframe_sleep.get(timeframe, 3600)  # Default 1 hour

    def get_strategy_status(self) -> Dict:
        """Get status of all strategies"""
        status = {}

        for name, strategy in self.strategies.items():
            status[name] = {
                'enabled': strategy.config.enabled,
                'active': strategy.active,
                'positions': len(strategy.positions),
                'symbols': strategy.config.symbols,
                'timeframe': strategy.config.timeframe,
                'risk_percentage': float(strategy.config.risk_percentage)
            }

        return status

    def get_all_positions(self) -> Dict:
        """Get all active positions"""
        return self.active_positions

    def enable_strategy(self, strategy_name: str) -> bool:
        """Enable a strategy"""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].config.enabled = True
            self.strategies[strategy_name].active = True
            logger.info(f"✅ Enabled {strategy_name} strategy")
            return True
        return False

    def disable_strategy(self, strategy_name: str) -> bool:
        """Disable a strategy"""
        if strategy_name in self.strategies:
            self.strategies[strategy_name].config.enabled = False
            self.strategies[strategy_name].active = False
            logger.info(f"❌ Disabled {strategy_name} strategy")
            return True
        return False

    async def _force_immediate_trades(self):
        """Force immediate trades for testing purposes"""
        try:
            logger.info("🎯 Forcing immediate test trades...")

            # Force BTC buy trade
            await self._force_trade("BTC/USDT", "buy", 10.0)
            await asyncio.sleep(2)

            # Force ETH buy trade
            await self._force_trade("ETH/USDT", "buy", 15.0)
            await asyncio.sleep(2)

            # Force SOL buy trade
            await self._force_trade("SOL/USDT", "buy", 8.0)

        except Exception as e:
            logger.error(f"Error forcing immediate trades: {e}")

    async def _force_trade(self, symbol: str, side: str, amount_usd: float):
        """Force execute a single trade"""
        try:
            # Get current price
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                logger.error(f"No ticker data for {symbol}")
                return

            # Use best ticker
            best_ticker = max(tickers.values(), key=lambda t: t.volume)
            current_price = float(best_ticker.last)

            # Calculate amount to trade
            crypto_amount = amount_usd / current_price

            # Find best exchange
            best_exchange_result = await self.exchange_manager.find_best_price(symbol, side)
            if not best_exchange_result:
                logger.error(f"No exchange available for {symbol}")
                return

            best_exchange, best_price = best_exchange_result

            # Try to create real order
            try:
                order = await self.exchange_manager.create_order(
                    exchange_name=best_exchange,
                    order_type="market",
                    side=side,
                    symbol=symbol,
                    amount=crypto_amount
                )

                if order:
                    logger.info(f"✅ FORCED TRADE EXECUTED: {side.upper()} {crypto_amount:.8f} {symbol} at ${current_price:.2f} on {best_exchange.upper()}")
                    logger.info(f"📋 Order ID: {order.id}, Status: {order.status}")
                else:
                    logger.warning(f"⚠️ SIMULATED TRADE: {side.upper()} {crypto_amount:.8f} {symbol} at ${current_price:.2f} on {best_exchange.upper()}")

            except Exception as order_error:
                logger.error(f"❌ TRADE FAILED: {side.upper()} {symbol} - {str(order_error)}")

        except Exception as e:
            logger.error(f"Error in force trade: {e}")
