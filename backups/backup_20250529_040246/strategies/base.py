"""
Verbeterde base trading strategy interface
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from decimal import Decimal
from enum import Enum
import time
import asyncio
from loguru import logger

class SignalType(Enum):
    """Trading signal types"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    SCALE_IN = "scale_in"
    SCALE_OUT = "scale_out"

class StrategyState(Enum):
    """Strategy execution states"""
    INACTIVE = "inactive"
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"

@dataclass
class TradingSignal:
    """
    Verbeterde trading signal met uitgebreide metadata
    """
    signal: SignalType
    confidence: float  # 0.0 to 1.0
    price: Decimal
    symbol: str = ""
    amount: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    reasoning: str = ""
    timestamp: float = field(default_factory=time.time)
    strategy_name: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

    def is_valid(self) -> bool:
        """Check if signal is valid"""
        return (
            self.confidence >= 0.3 and
            self.price > 0 and
            self.signal in [SignalType.BUY, SignalType.SELL, SignalType.HOLD]
        )

    def get_risk_reward_ratio(self) -> float:
        """Calculate risk/reward ratio"""
        if not self.stop_loss or not self.take_profit:
            return 0.0

        risk = abs(float(self.price - self.stop_loss))
        reward = abs(float(self.take_profit - self.price))

        return reward / risk if risk > 0 else 0.0

@dataclass
class Position:
    """
    Verbeterde trading position met risk management
    """
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: Decimal
    amount: Decimal
    current_price: Decimal
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    trailing_stop: Optional[Decimal] = None
    pnl: Decimal = Decimal('0')
    pnl_percentage: Decimal = Decimal('0')
    timestamp: float = field(default_factory=time.time)
    order_id: str = ""
    fees_paid: Decimal = Decimal('0')
    max_price: Decimal = Decimal('0')  # Voor trailing stop tracking
    min_price: Decimal = Decimal('999999')  # Voor trailing stop tracking

    def update_price(self, new_price: Decimal):
        """Update current price en bereken PnL"""
        self.current_price = new_price

        # Update max/min prices voor trailing stop
        if new_price > self.max_price:
            self.max_price = new_price
        if new_price < self.min_price:
            self.min_price = new_price

        # Bereken PnL
        if self.side == 'long':
            self.pnl = (new_price - self.entry_price) * self.amount
            self.pnl_percentage = ((new_price - self.entry_price) / self.entry_price) * 100
        else:  # short
            self.pnl = (self.entry_price - new_price) * self.amount
            self.pnl_percentage = ((self.entry_price - new_price) / self.entry_price) * 100

    def should_trigger_stop_loss(self) -> bool:
        """Check of stop loss moet worden getriggerd"""
        if not self.stop_loss:
            return False

        if self.side == 'long':
            return self.current_price <= self.stop_loss
        else:
            return self.current_price >= self.stop_loss

    def should_trigger_take_profit(self) -> bool:
        """Check of take profit moet worden getriggerd"""
        if not self.take_profit:
            return False

        if self.side == 'long':
            return self.current_price >= self.take_profit
        else:
            return self.current_price <= self.take_profit

@dataclass
class StrategyConfig:
    """
    Uitgebreide strategy configuratie met geavanceerde instellingen
    """
    name: str
    enabled: bool = True
    risk_percentage: Decimal = Decimal('2')  # % of portfolio per trade
    max_positions: int = 3
    stop_loss_percentage: Decimal = Decimal('2')  # % stop loss
    take_profit_percentage: Decimal = Decimal('6')  # % take profit
    trailing_stop_percentage: Decimal = Decimal('1')  # % trailing stop
    min_confidence: Decimal = Decimal('0.3')  # Minimum signal confidence
    timeframe: str = "1h"
    symbols: List[str] = field(default_factory=lambda: ["BTC/USDT", "ETH/USDT"])

    # Geavanceerde risk management
    max_daily_trades: int = 10
    max_consecutive_losses: int = 3
    cooldown_after_loss: int = 300  # seconds
    position_sizing_method: str = "risk_based"  # "risk_based", "fixed", "percentage"

    # Performance tracking
    track_performance: bool = True
    performance_window: int = 100  # Last N trades
    min_risk_reward_ratio: float = 1.5  # Minimum R:R ratio

    # Scaling instellingen
    enable_scaling: bool = False
    scale_in_levels: List[Decimal] = field(default_factory=lambda: [Decimal('0.5'), Decimal('1.0')])
    scale_out_levels: List[Decimal] = field(default_factory=lambda: [Decimal('0.5'), Decimal('1.0')])

    # Timing instellingen
    analysis_interval: int = 300  # seconds between analysis
    max_position_hold_time: int = 86400  # 24 hours max hold time

    def validate(self) -> bool:
        """Valideer configuratie instellingen"""
        if self.risk_percentage <= 0 or self.risk_percentage > 10:
            logger.error("Risk percentage must be between 0 and 10%")
            return False

        if self.min_confidence < 0 or self.min_confidence > 1:
            logger.error("Min confidence must be between 0 and 1")
            return False

        if not self.symbols:
            logger.error("At least one symbol must be configured")
            return False

        return True

class BaseStrategy(ABC):
    """
    Verbeterde base class voor alle trading strategieën

    Deze klasse biedt een robuuste basis voor strategy implementaties
    met geavanceerde risk management en performance tracking.
    """

    def __init__(self, config: StrategyConfig):
        self.config = config
        self.positions: Dict[str, Position] = {}
        self.state = StrategyState.INACTIVE
        self.last_analysis_time = 0
        self.consecutive_losses = 0
        self.daily_trades = 0
        self.last_trade_time = 0
        self.performance_history: List[Dict] = []
        self.error_count = 0
        self.last_error_time = 0

        # Valideer configuratie
        if not self.config.validate():
            raise ValueError(f"Invalid configuration for strategy {self.config.name}")

        logger.info(f"✅ Strategy {self.config.name} initialized")

    @abstractmethod
    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """
        Analyseer marktdata en genereer trading signal

        Args:
            symbol: Trading pair symbol (e.g., "BTC/USDT")
            market_data: Dictionary met marktdata (price, volume, etc.)

        Returns:
            TradingSignal met signal type, confidence, en metadata
        """
        pass

    @abstractmethod
    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """
        Bepaal of we een nieuwe positie moeten openen

        Args:
            signal: Generated trading signal
            portfolio_value: Current portfolio value

        Returns:
            True als positie moet worden geopend
        """
        pass

    @abstractmethod
    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """
        Bepaal of we een bestaande positie moeten sluiten

        Args:
            position: Current position
            current_price: Current market price

        Returns:
            True als positie moet worden gesloten
        """
        pass

    def start(self):
        """Start de strategy"""
        if self.state == StrategyState.ERROR:
            logger.warning(f"⚠️ Strategy {self.config.name} in error state, resetting...")
            self.error_count = 0

        self.state = StrategyState.ACTIVE
        logger.info(f"🚀 Strategy {self.config.name} started")

    def stop(self):
        """Stop de strategy"""
        self.state = StrategyState.INACTIVE
        logger.info(f"🛑 Strategy {self.config.name} stopped")

    def pause(self):
        """Pauzeer de strategy"""
        self.state = StrategyState.PAUSED
        logger.info(f"⏸️ Strategy {self.config.name} paused")

    def resume(self):
        """Hervat de strategy"""
        if self.state == StrategyState.PAUSED:
            self.state = StrategyState.ACTIVE
            logger.info(f"▶️ Strategy {self.config.name} resumed")

    def is_active(self) -> bool:
        """Check of strategy actief is"""
        return self.state == StrategyState.ACTIVE

    def can_trade(self) -> bool:
        """Check of strategy kan handelen"""
        if not self.is_active():
            return False

        # Check daily trade limit
        if self.daily_trades >= self.config.max_daily_trades:
            return False

        # Check consecutive losses
        if self.consecutive_losses >= self.config.max_consecutive_losses:
            return False

        # Check cooldown after loss
        if (time.time() - self.last_trade_time) < self.config.cooldown_after_loss:
            return False

        return True

    async def calculate_position_size(self, signal: TradingSignal, portfolio_value: Decimal) -> Decimal:
        """Calculate position size based on risk management"""
        risk_amount = portfolio_value * (self.config.risk_percentage / 100)

        if signal.stop_loss:
            price_diff = abs(signal.price - signal.stop_loss)
            if price_diff > 0:
                position_size = risk_amount / price_diff
                return min(position_size, portfolio_value * Decimal('0.1'))  # Max 10% of portfolio

        # Fallback: use fixed percentage
        return portfolio_value * (self.config.risk_percentage / 100) / signal.price

    async def update_trailing_stop(self, position: Position, current_price: Decimal) -> Optional[Decimal]:
        """Update trailing stop loss"""
        if not position.trailing_stop:
            return None

        trailing_percentage = self.config.trailing_stop_percentage / 100

        if position.side == 'long':
            # For long positions, trailing stop moves up with price
            new_stop = current_price * (1 - trailing_percentage)
            if position.stop_loss is None or new_stop > position.stop_loss:
                return new_stop
        else:
            # For short positions, trailing stop moves down with price
            new_stop = current_price * (1 + trailing_percentage)
            if position.stop_loss is None or new_stop < position.stop_loss:
                return new_stop

        return position.stop_loss

    async def calculate_pnl(self, position: Position, current_price: Decimal) -> tuple:
        """Calculate PnL for a position"""
        if position.side == 'long':
            pnl = (current_price - position.entry_price) * position.amount
            pnl_percentage = ((current_price - position.entry_price) / position.entry_price) * 100
        else:
            pnl = (position.entry_price - current_price) * position.amount
            pnl_percentage = ((position.entry_price - current_price) / position.entry_price) * 100

        return pnl, pnl_percentage

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information"""
        return {
            "name": self.config.name,
            "enabled": self.config.enabled,
            "active": self.active,
            "positions": len(self.positions),
            "symbols": self.config.symbols,
            "risk_percentage": float(self.config.risk_percentage),
            "timeframe": self.config.timeframe
        }
