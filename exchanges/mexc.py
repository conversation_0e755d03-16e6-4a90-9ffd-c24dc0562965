"""
MEXC exchange implementation
"""

from decimal import Decimal
from typing import Dict, List, Optional, Any

import ccxt
from loguru import logger

from .base import Balance, BaseExchange, Order, Ticker


class MEXCExchange(BaseExchange):
    """MEXC exchange implementation"""

    def __init__(self, api_key: str, secret_key: str, sandbox: bool = False):
        super().__init__(api_key, secret_key, None, sandbox)
        self.exchange_name = "MEXC"

    async def connect(self) -> bool:
        """Connect to MEXC exchange"""
        try:
            logger.info(f"Attempting to connect to {self.exchange_name}...")
            self.exchange = ccxt.mexc(
                {
                    "apiKey": self.api_key,
                    "secret": self.secret_key,
                    "sandbox": self.sandbox,
                    "enableRateLimit": True,
                }
            )

            # Test connection using async method
            logger.info(f"Loading markets for {self.exchange_name}...")
            # MEXC load_markets returns a dict, not awaitable
            self.exchange.load_markets()
            logger.info(f"Successfully connected to {self.exchange_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to {self.exchange_name}: {e}")
            # Log additional error details if available
            logger.error(f"Exception type: {type(e)}")
            if hasattr(e, "args") and e.args:
                logger.error(f"Exception args: {e.args}")
            return False

    async def get_balance(self) -> Dict[str, Balance]:
        """Get account balances"""
        try:
            # Try to fetch balance with proper error handling
            balance_data = None
            try:
                balance_data = self.exchange.fetch_balance()
            except Exception as fetch_error:
                logger.error(f"Failed to fetch balance from {self.exchange_name}: {fetch_error}")
                return {}

            if not balance_data:
                logger.warning(f"No balance data received from {self.exchange_name}")
                return {}

            balances = {}

            # Handle different response formats
            if isinstance(balance_data, dict):
                for currency, data in balance_data.items():
                    if currency not in ["info", "free", "used", "total"] and isinstance(data, dict):
                        # Check if data has the expected structure
                        if data and isinstance(data, dict):
                            try:
                                balances[currency] = Balance(
                                    currency=currency,
                                    free=Decimal(str(data.get("free", 0))),
                                    used=Decimal(str(data.get("used", 0))),
                                    total=Decimal(str(data.get("total", 0))),
                                )
                            except Exception as balance_error:
                                logger.warning(f"Error processing balance for {currency}: {balance_error}")
                                continue

            logger.info(f"Successfully fetched {len(balances)} balances from {self.exchange_name}")
            return balances

        except Exception as e:
            logger.error(f"Error fetching balance from {self.exchange_name}: {e}")
            return {}  # Return empty dict instead of raising

    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker information"""
        try:
            ticker_data = self.exchange.fetch_ticker(symbol)

            return Ticker(
                symbol=symbol,
                bid=Decimal(str(ticker_data["bid"])) if ticker_data["bid"] else Decimal("0"),
                ask=Decimal(str(ticker_data["ask"])) if ticker_data["ask"] else Decimal("0"),
                last=Decimal(str(ticker_data["last"])) if ticker_data["last"] else Decimal("0"),
                high=Decimal(str(ticker_data["high"])) if ticker_data["high"] else Decimal("0"),
                low=Decimal(str(ticker_data["low"])) if ticker_data["low"] else Decimal("0"),
                volume=Decimal(str(ticker_data["baseVolume"])) if ticker_data["baseVolume"] else Decimal("0"),
                timestamp=int(ticker_data["timestamp"]) if ticker_data["timestamp"] else 0,
            )

        except Exception as e:
            logger.error(f"Error fetching ticker for {symbol} from {self.exchange_name}: {e}")
            raise

    async def create_market_buy_order(self, symbol: str, amount: Decimal) -> Order:
        """Create market buy order - MEXC requires cost instead of quantity"""
        try:
            logger.info(f"Creating MEXC market buy order: symbol={symbol}, amount={amount}")

            # MEXC requires cost (USDT amount) instead of quantity for market buy orders
            # Get current price to calculate cost
            ticker = await self.get_ticker(symbol)
            current_price = ticker.last
            cost = float(amount) * float(current_price)

            logger.info(f"MEXC market buy: cost=${cost:.2f} at price=${current_price:.2f}")

            # Use createMarketBuyOrderRequiresPrice=False and pass cost as amount
            order_data = self.exchange.create_market_buy_order(
                symbol,
                cost,  # Pass cost instead of quantity
                None,  # No price needed
            )

            logger.info(f"MEXC order data received: {order_data}")
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating market buy order on {self.exchange_name}: {e}")
            raise

    async def create_market_sell_order(self, symbol: str, amount: Decimal) -> Order:
        """Create market sell order"""
        try:
            order_data = self.exchange.create_market_sell_order(symbol, float(amount))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating market sell order on {self.exchange_name}: {e}")
            raise

    async def create_limit_buy_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create limit buy order"""
        try:
            order_data = self.exchange.create_limit_buy_order(symbol, float(amount), float(price))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating limit buy order on {self.exchange_name}: {e}")
            raise

    async def create_limit_sell_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create limit sell order"""
        try:
            order_data = self.exchange.create_limit_sell_order(symbol, float(amount), float(price))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating limit sell order on {self.exchange_name}: {e}")
            raise

    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get open orders"""
        try:
            orders_data = self.exchange.fetch_open_orders(symbol)
            return [self._parse_order(order) for order in orders_data]

        except Exception as e:
            logger.error(f"Error fetching open orders from {self.exchange_name}: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        try:
            self.exchange.cancel_order(order_id, symbol)
            logger.info(f"Successfully cancelled order {order_id} on {self.exchange_name}")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order {order_id} on {self.exchange_name}: {e}")
            return False

    async def get_order_status(self, order_id: str, symbol: str) -> Order:
        """Get order status"""
        try:
            order_data = self.exchange.fetch_order(order_id, symbol)
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error fetching order status from {self.exchange_name}: {e}")
            raise

    def _parse_order(self, order_data: Any) -> Order:
        """Parse order data from exchange format to our Order format"""
        return Order(
            id=order_data["id"],
            symbol=order_data["symbol"],
            side=order_data["side"],
            amount=Decimal(str(order_data["amount"])),
            price=Decimal(str(order_data["price"])) if order_data["price"] else Decimal("0"),
            status=order_data["status"],
            timestamp=int(order_data["timestamp"]) if order_data["timestamp"] else 0,
            filled=Decimal(str(order_data["filled"])) if order_data["filled"] else Decimal("0"),
            remaining=Decimal(str(order_data["remaining"])) if order_data["remaining"] else Decimal("0"),
            cost=Decimal(str(order_data["cost"])) if order_data["cost"] else Decimal("0"),
            fee=order_data.get("fee"),
        )
