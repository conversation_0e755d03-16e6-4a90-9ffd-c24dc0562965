"""
KuCoin exchange implementation
"""

from decimal import Decimal
from typing import Dict, List, Optional, Any

import ccxt.async_support as ccxt
from loguru import logger

from .base import Balance, BaseExchange, Order, Ticker


class KuCoinExchange(BaseExchange):
    """KuCoin exchange implementation"""

    def __init__(self, api_key: str, secret_key: str, passphrase: str, sandbox: bool = False):
        super().__init__(api_key, secret_key, passphrase, sandbox)
        self.exchange_name = "KuCoin"

    async def connect(self) -> bool:
        """Connect to KuCoin exchange"""
        try:
            self.exchange = ccxt.kucoin(
                {
                    "apiKey": self.api_key,
                    "secret": self.secret_key,
                    "password": self.passphrase,
                    "sandbox": self.sandbox,
                    "enableRateLimit": True,
                }
            )

            # Test connection using async method
            # load_markets() is not async in CCXT
            self.exchange.load_markets()
            logger.info(f"Successfully connected to {self.exchange_name}")

            # DON'T close the connection - keep it open for use
            # await self.exchange.close()
            return True

        except Exception as e:
            logger.error(f"Failed to connect to {self.exchange_name}: {e}")
            return False

    async def get_balance(self) -> Dict[str, Balance]:
        """Get account balances"""
        try:
            balance_data = await self.exchange.fetch_balance()
            if not balance_data:
                logger.warning(f"No balance data received from {self.exchange_name}")
                return {}

            # Process balance data
            balances = {}
            for currency, data in balance_data["total"].items():
                balances[currency] = Balance(
                    currency=currency,
                    total=Decimal(str(data)),
                    free=Decimal(str(balance_data["free"][currency])),
                    used=Decimal(str(balance_data["used"][currency])),
                )
            return balances

        except Exception as fetch_error:
            logger.error(f"Failed to fetch balance from {self.exchange_name}: {fetch_error}")
            return {}

    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker information"""
        try:
            ticker_data = await self.exchange.fetch_ticker(symbol)

            return Ticker(
                symbol=symbol,
                bid=Decimal(str(ticker_data["bid"])) if ticker_data["bid"] else Decimal("0"),
                ask=Decimal(str(ticker_data["ask"])) if ticker_data["ask"] else Decimal("0"),
                last=Decimal(str(ticker_data["last"])) if ticker_data["last"] else Decimal("0"),
                high=Decimal(str(ticker_data["high"])) if ticker_data["high"] else Decimal("0"),
                low=Decimal(str(ticker_data["low"])) if ticker_data["low"] else Decimal("0"),
                volume=Decimal(str(ticker_data["baseVolume"])) if ticker_data["baseVolume"] else Decimal("0"),
                timestamp=int(ticker_data["timestamp"]) if ticker_data["timestamp"] else 0,
            )

        except Exception as e:
            logger.error(f"Error fetching ticker for {symbol} from {self.exchange_name}: {e}")
            raise

    async def create_market_buy_order(self, symbol: str, amount: Decimal) -> Order:
        """Create market buy order"""
        try:
            logger.info(f"Creating market buy order: symbol={symbol}, amount={amount}")

            # For KuCoin market buy orders, amount represents the quote currency (USDT)
            order_data = await self.exchange.create_market_buy_order(
                symbol=symbol,
                amount=float(amount),  # This will be the quote amount (USDT)
            )

            logger.info(f"Order data received: {order_data}")
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating market buy order on {self.exchange_name}: {e}")
            logger.error(f"Exception type: {type(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    async def create_market_sell_order(self, symbol: str, amount: Decimal) -> Order:
        """Create market sell order"""
        try:
            order_data = await self.exchange.create_market_sell_order(symbol, float(amount))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating market sell order on {self.exchange_name}: {e}")
            raise

    async def create_limit_buy_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create limit buy order"""
        try:
            order_data = await self.exchange.create_limit_buy_order(symbol, float(amount), float(price))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating limit buy order on {self.exchange_name}: {e}")
            raise

    async def create_limit_sell_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create limit sell order"""
        try:
            order_data = await self.exchange.create_limit_sell_order(symbol, float(amount), float(price))
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error creating limit sell order on {self.exchange_name}: {e}")
            raise

    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get open orders"""
        try:
            orders_data = await self.exchange.fetch_open_orders(symbol)
            # CCXT returns dictionaries, not Order objects
            return [self._parse_order(order) for order in orders_data]

        except Exception as e:
            logger.error(f"Error fetching open orders from {self.exchange_name}: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        try:
            await self.exchange.cancel_order(order_id, symbol)
            logger.info(f"Successfully cancelled order {order_id} on {self.exchange_name}")
            return True

        except Exception as e:
            logger.error(f"Error cancelling order {order_id} on {self.exchange_name}: {e}")
            return False

    async def get_order_status(self, order_id: str, symbol: str) -> Order:
        """Get order status"""
        try:
            order_data = await self.exchange.fetch_order(order_id, symbol)
            return self._parse_order(order_data)

        except Exception as e:
            logger.error(f"Error fetching order status from {self.exchange_name}: {e}")
            raise

    def _parse_order(self, order_data: Any) -> Order:
        """Parse order data from exchange format to our Order format"""
        logger.info(f"Parsing order data: {order_data}")

        def safe_decimal(value, field_name="unknown"):
            """Safely convert value to Decimal"""
            logger.debug(f"Converting {field_name}: {value} (type: {type(value)})")
            if value is None or value == "":
                return Decimal("0")
            try:
                result = Decimal(str(value))
                logger.debug(f"Successfully converted {field_name}: {result}")
                return result
            except Exception as e:
                logger.error(f"Failed to convert {field_name} '{value}' to Decimal: {e}")
                return Decimal("0")

        try:
            order = Order(
                id=str(order_data.get("id", "")),
                symbol=str(order_data.get("symbol", "")),
                side=str(order_data.get("side", "")),
                amount=safe_decimal(order_data.get("amount"), "amount"),
                price=safe_decimal(order_data.get("price"), "price"),
                status=str(order_data.get("status", "")),
                timestamp=int(order_data["timestamp"]) if order_data.get("timestamp") else 0,
                filled=safe_decimal(order_data.get("filled"), "filled"),
                remaining=safe_decimal(order_data.get("remaining"), "remaining"),
                cost=safe_decimal(order_data.get("cost"), "cost"),
                fee=order_data.get("fee"),
            )
            logger.info(f"Successfully parsed order: {order.id}")
            return order
        except Exception as e:
            logger.error(f"Error parsing order data: {e}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
