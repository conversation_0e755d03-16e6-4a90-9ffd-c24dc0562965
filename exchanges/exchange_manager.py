import asyncio
import logging
import os
from typing import Dict, List, Optional, Any
import ccxt.async_support as ccxt

logger = logging.getLogger(__name__)

class ExchangeManager:
    """Manages connections to multiple cryptocurrency exchanges"""

    def __init__(self):
        self.exchanges = {}
        self.initialized = False

    async def initialize(self):
        """Initialize exchange connections"""
        try:
            logger.info("🔗 Initializing exchange connections...")

            # Initialize KuCoin
            if all([os.getenv('KUCOIN_API_KEY'), os.getenv('KUCOIN_SECRET_KEY'), os.getenv('KUCOIN_PASSPHRASE')]):
                try:
                    kucoin = ccxt.kucoin({
                        'apiKey': os.getenv('KUCOIN_API_KEY'),
                        'secret': os.getenv('KUCOIN_SECRET_KEY'),
                        'password': os.getenv('KUCOIN_PASSPHRASE'),
                        'sandbox': os.getenv('KUCOIN_SANDBOX', 'false').lower() == 'true',
                        'enableRateLimit': True,
                    })

                    # Test connection
                    await kucoin.load_markets()
                    self.exchanges['kucoin'] = kucoin
                    logger.info("✅ KuCoin connected")

                except Exception as e:
                    logger.error(f"❌ KuCoin connection failed: {e}")

            # Initialize MEXC
            if all([os.getenv('MEXC_API_KEY'), os.getenv('MEXC_SECRET_KEY')]):
                try:
                    mexc = ccxt.mexc({
                        'apiKey': os.getenv('MEXC_API_KEY'),
                        'secret': os.getenv('MEXC_SECRET_KEY'),
                        'sandbox': os.getenv('MEXC_SANDBOX', 'false').lower() == 'true',
                        'enableRateLimit': True,
                    })

                    # Test connection
                    await mexc.load_markets()
                    self.exchanges['mexc'] = mexc
                    logger.info("✅ MEXC connected")

                except Exception as e:
                    logger.error(f"❌ MEXC connection failed: {e}")

            if not self.exchanges:
                logger.warning("⚠️ No exchanges connected")
                return False

            self.initialized = True
            logger.info(f"✅ {len(self.exchanges)} exchanges initialized")
            return True

        except Exception as e:
            logger.error(f"❌ Exchange initialization failed: {e}")
            return False

    async def get_ticker_from_all(self, symbol: str) -> Dict:
        """Get ticker from all exchanges"""
        tickers = {}

        for name, exchange in self.exchanges.items():
            try:
                ticker = await exchange.fetch_ticker(symbol)
                tickers[name] = ticker
            except Exception as e:
                logger.debug(f"Failed to get ticker from {name}: {e}")

        return tickers

    async def get_all_balances(self) -> Dict:
        """Get balances from all exchanges"""
        all_balances = {}

        for name, exchange in self.exchanges.items():
            try:
                balance = await exchange.fetch_balance()
                # Filter out zero balances
                filtered_balance = {k: v for k, v in balance['total'].items() if v > 0}
                all_balances[name] = filtered_balance
                logger.info(f"✅ Got {len(filtered_balance)} balances from {name}")
            except Exception as e:
                logger.error(f"❌ Failed to get balance from {name}: {e}")
                all_balances[name] = {}

        return all_balances

    async def close_all(self):
        """Close all exchange connections"""
        for name, exchange in self.exchanges.items():
            try:
                await exchange.close()
                logger.info(f"✅ Closed {name} connection")
            except Exception as e:
                logger.error(f"❌ Error closing {name}: {e}")

        self.exchanges.clear()
        self.initialized = False
