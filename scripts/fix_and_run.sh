#!/bin/bash
echo "🚀 DEFINITIEVE FIX voor MyOwnMoneyMaker Trading Bot"
echo "=================================================="
echo ""

# 1. Stop alles
echo "1️⃣ Stopping bot..."
pkill -f "python.*telegram" 2>/dev/null || true

# 2. Clean install in conda base
echo "2️⃣ Installing packages..."
cd ~/Desktop/Myownmoneymaker

# Essentiële packages installeren
pip install --force-reinstall httpx==0.26.0
pip install --force-reinstall python-telegram-bot==20.8
pip install ccxt pandas pandas_ta loguru aiohttp python-dotenv

# 3. Start de bot
echo "3️⃣ Starting bot..."
python3 telegram_simple.py > logs/bot.log 2>&1 &

# 4. Check status
sleep 3
if pgrep -f "python.*telegram_simple" > /dev/null; then
    echo "✅ Bo<PERSON> is running!"
    echo ""
    echo "📱 Open Telegram en check je bot"
    echo "📊 View logs: tail -f logs/bot.log"
    echo "🛑 Stop bot: pkill -f telegram_simple"
else
    echo "❌ <PERSON><PERSON> failed to start. Checking error..."
    tail -20 logs/bot.log
fi
