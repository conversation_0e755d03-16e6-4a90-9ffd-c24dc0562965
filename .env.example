import os
import logging
from typing import List, Optional, Dict, Any
from pydantic import BaseSettings, validator, Field, SecretStr, HttpUrl, conint, confloat

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    # Security
    encryption_key: SecretStr = Field(..., env='ENCRYPTION_KEY')

    # Telegram Bot
    telegram_bot_token: SecretStr = Field(..., env='TELEGRAM_BOT_TOKEN')
    telegram_admin_user_ids: List[int] = Field(..., env='TELEGRAM_ADMIN_USER_ID')

    # Exchange API Configurations
    kucoin_api_key: Optional[SecretStr] = Field(None, env='KUCOIN_API_KEY')
    kucoin_secret_key: Optional[SecretStr] = Field(None, env='KUCOIN_SECRET_KEY')
    kucoin_passphrase: Optional[SecretStr] = Field(None, env='KUCOIN_PASSPHRASE')
    kucoin_sandbox: bool = Field(True, env='KUCOIN_SANDBOX')

    mexc_api_key: Optional[SecretStr] = Field(None, env='MEXC_API_KEY')
    mexc_secret_key: Optional[SecretStr] = Field(None, env='MEXC_SECRET_KEY')
    mexc_sandbox: bool = Field(True, env='MEXC_SANDBOX')

    # AI & ML Model Settings
    ml_models_enabled: bool = Field(True, env='ML_MODELS_ENABLED')
    model_type: str = Field('LSTM', env='MODEL_TYPE')
    prediction_horizon: conint(gt=0) = Field(24, env='PREDICTION_HORIZON')
    retraining_interval: conint(gt=0) = Field(168, env='RETRAINING_INTERVAL')

    multi_model_enabled: bool = Field(True, env='MULTI_MODEL_ENABLED')
    models: List[str] = Field(['GPT4', 'CLAUDE', 'GEMINI', 'LLAMA'], env='MODELS')
    consensus_threshold: confloat(ge=0, le=1) = Field(0.75, env='CONSENSUS_THRESHOLD')

    # Technical Analysis
    advanced_indicators_enabled: bool = Field(True, env='ADVANCED_INDICATORS_ENABLED')
    custom_indicators: List[str] = Field(['RSI', 'MACD', 'Bollinger', 'Ichimoku', 'Elliott'], env='CUSTOM_INDICATORS')
    indicator_timeframes: List[str] = Field(['1h', '4h', '1d'], env='INDICATOR_TIMEFRAMES')

    # Sentiment Analysis
    sentiment_analysis_enabled: bool = Field(True, env='SENTIMENT_ANALYSIS_ENABLED')
    sentiment_weight: confloat(ge=0, le=1) = Field(0.3, env='SENTIMENT_WEIGHT')

    twitter_bearer_token: Optional[SecretStr] = Field(None, env='TWITTER_BEARER_TOKEN')
    twitter_api_key: Optional[SecretStr] = Field(None, env='TWITTER_API_KEY')
    twitter_api_secret: Optional[SecretStr] = Field(None, env='TWITTER_API_SECRET')
    twitter_access_token: Optional[SecretStr] = Field(None, env='TWITTER_ACCESS_TOKEN')
    twitter_access_token_secret: Optional[SecretStr] = Field(None, env='TWITTER_ACCESS_TOKEN_SECRET')

    news_api_key: Optional[SecretStr] = Field(None, env='NEWS_API_KEY')
    news_max_length: conint(gt=0) = Field(150, env='NEWS_MAX_LENGTH')

    # On-chain & Blockchain
    onchain_analysis_enabled: bool = Field(True, env='ONCHAIN_ANALYSIS_ENABLED')
    etherscan_api_key: Optional[SecretStr] = Field(None, env='ETHERSCAN_API_KEY')
    whale_alert_threshold: conint(ge=0) = Field(1_000_000, env='WHALE_ALERT_THRESHOLD')

    # Economic Data & Correlation
    correlation_analysis_enabled: bool = Field(True, env='CORRELATION_ANALYSIS_ENABLED')
    correlated_assets: List[str] = Field(['BTC', 'ETH', 'TOTAL_MARKET_CAP', 'DXY', 'SPX'], env='CORRELATED_ASSETS')
    correlation_threshold: confloat(ge=0, le=1) = Field(0.7, env='CORRELATION_THRESHOLD')

    economic_indicators_enabled: bool = Field(True, env='ECONOMIC_INDICATORS_ENABLED')
    alpha_vantage_api_key: Optional[SecretStr] = Field(None, env='ALPHA_VANTAGE_API_KEY')
    fred_api_key: Optional[SecretStr] = Field(None, env='FRED_API_KEY')
    monitored_indicators: List[str] = Field(['CPI', 'UNEMPLOYMENT', 'FED_RATE', 'GDP'], env='MONITORED_INDICATORS')

    # Anomaly Detection
    anomaly_detection_enabled: bool = Field(True, env='ANOMALY_DETECTION_ENABLED')
    anomaly_sensitivity: confloat(ge=0, le=1) = Field(0.8, env='ANOMALY_SENSITIVITY')
    anomaly_response: str = Field('alert', env='ANOMALY_RESPONSE')

    # Database & Logging
    database_url: HttpUrl = Field(..., env='DATABASE_URL')
    log_level: str = Field('INFO', env='LOG_LEVEL')
    log_file: str = Field('logs/trading_bot.log', env='LOG_FILE')

    @validator('telegram_admin_user_ids', pre=True)
    def parse_telegram_admin_user_ids(cls, v):
        if isinstance(v, str):
            try:
                ids = [int(i.strip()) for i in v.split(',') if i.strip().isdigit()]
                if not ids:
                    raise ValueError("No valid telegram admin user IDs found")
                return ids
            except Exception as e:
                logger.error(f"Invalid TELEGRAM_ADMIN_USER_ID value: {v}")
                raise ValueError(f"Invalid TELEGRAM_ADMIN_USER_ID value: {v}") from e
        elif isinstance(v, list):
            return v
        else:
            raise ValueError("TELEGRAM_ADMIN_USER_ID must be a comma-separated string or list of ints")

    @validator('models', pre=True)
    def parse_models(cls, v):
        if isinstance(v, str):
            return [model.strip().upper() for model in v.split(',') if model.strip()]
        elif isinstance(v, list):
            return [str(model).upper() for model in v]
        else:
            raise ValueError("MODELS must be a comma-separated string or list of strings")

    @validator('custom_indicators', 'indicator_timeframes', 'correlated_assets', 'monitored_indicators', pre=True)
    def parse_list_fields(cls, v):
        if isinstance(v, str):
            return [item.strip() for item in v.split(',') if item.strip()]
        elif isinstance(v, list):
            return v
        else:
            raise ValueError("Field must be a comma-separated string or list")

    @validator('anomaly_response')
    def validate_anomaly_response(cls, v):
        valid_responses = {'alert', 'pause', 'hedge'}
        if v.lower() not in valid_responses:
            raise ValueError(f"ANOMALY_RESPONSE must be one of {valid_responses}")
        return v.lower()

    def __repr__(self):
        # Avoid printing secrets in logs
        safe_dict = self.dict()
        for key, value in safe_dict.items():
            if isinstance(value, SecretStr):
                safe_dict[key] = "********"
        return f"<Settings {safe_dict}>"

    class Config:
        env_file = '.env'
        env_file_encoding = 'utf-8'
        case_sensitive = False
        validate_assignment = True


settings = Settings()
