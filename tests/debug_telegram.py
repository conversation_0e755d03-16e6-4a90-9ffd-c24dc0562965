#!/usr/bin/env python3
"""
Debug script to test Telegram bot connectivity
"""
import asyncio
import aiohttp
import json
import os
from dotenv import load_dotenv

load_dotenv()

async def test_telegram_bot():
    """Test basic Telegram bot functionality"""
    
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    if not bot_token:
        print("❌ No TELEGRAM_BOT_TOKEN found in .env")
        return
    
    base_url = f"https://api.telegram.org/bot{bot_token}"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Get bot info
        print("🔍 Testing bot info...")
        async with session.get(f"{base_url}/getMe") as response:
            data = await response.json()
            if data.get('ok'):
                bot_info = data['result']
                print(f"✅ Bot: @{bot_info['username']} ({bot_info['first_name']})")
            else:
                print(f"❌ Bot info failed: {data}")
                return
        
        # Test 2: Get updates
        print("\n🔍 Testing updates...")
        async with session.get(f"{base_url}/getUpdates") as response:
            data = await response.json()
            if data.get('ok'):
                updates = data['result']
                print(f"✅ Updates received: {len(updates)} messages")
                
                if updates:
                    print("\n📨 Recent messages:")
                    for update in updates[-5:]:  # Show last 5
                        if 'message' in update:
                            msg = update['message']
                            user = msg['from']
                            text = msg.get('text', 'No text')
                            print(f"  User {user['id']} (@{user.get('username', 'no_username')}): {text}")
                        elif 'callback_query' in update:
                            cb = update['callback_query']
                            user = cb['from']
                            data_text = cb['data']
                            print(f"  Callback from {user['id']}: {data_text}")
                else:
                    print("  No messages found")
            else:
                print(f"❌ Updates failed: {data}")
        
        # Test 3: Send test message to admin
        admin_ids_str = os.getenv("TELEGRAM_ADMIN_USER_ID", "")
        if admin_ids_str:
            admin_ids = [int(id.strip()) for id in admin_ids_str.split(",") if id.strip()]
            if admin_ids:
                admin_id = admin_ids[0]
                print(f"\n🔍 Testing message send to admin {admin_id}...")
                
                test_message = "🤖 **Debug Test**\n\nDit is een test bericht van de debug script!"
                
                payload = {
                    "chat_id": admin_id,
                    "text": test_message,
                    "parse_mode": "Markdown"
                }
                
                async with session.post(f"{base_url}/sendMessage", json=payload) as response:
                    data = await response.json()
                    if data.get('ok'):
                        print("✅ Test message sent successfully!")
                    else:
                        print(f"❌ Send message failed: {data}")

if __name__ == "__main__":
    asyncio.run(test_telegram_bot())
